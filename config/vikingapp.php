<?php

return [
    'portal_type' => [
        '' => 'All',
        0   => 'V-pro',
        1   => 'V-app'
    ],
    'type' => [
        0 => 'Alert',
        1 => 'Info',
        2 => 'Celebration',
        3 => 'Miscelleaneous'
    ],
    'gender' => [
        null => 'All',
        0 => 'Male',
        1 => 'Female',
        2 => 'Other'
    ],
    'user' => [
        0 => 'admin',
        1 => 'viking'
    ],
    'api' => [
        'domain' => env('VIKING_API_DOMAIN', 'https://viking-dev-admin-api-cdfwd8cqbbdaecan.francecentral-01.azurewebsites.net/'),
        'secret_key' => env('VIKING_API_SECRET_KEY', 'TMZJC9SZZXdr1itu6bl3ufugxdor0ygm'),
        'trigger-schedule' => env('VIKING_API_TRIGGER_SCHEDULE', 'api/Integration/trigger-schedule'),
        'send-mail' => env('VIKING_API_SEND_MAIL', 'api/Integration/send-mail'),

        'import' => env('VIKING_API_IMPORT', 'api/Integration/store/bulk-upload'),
        'validate' => env('VIKING_API_IMPORT', 'api/Integration/store/bulk-upload/validate'),
        'store-info' => env('VIKING_API_STORE_INFO', 'api/Integration/store/{id}/info'),
        'store-service' => env('VIKING_API_STORE_SERVICE', 'api/Integration/store/{id}/services'),
        'store-master-data' => env('VIKING_API_STORE_MASTER_DATA', 'api/Integration/store/master-data'),
        'store-categories' => env('VIKING_API_STORE_CATEGORIES', 'api/Integration/store/{id}/categories'),
        'store-category-questions' => env('VIKING_API_STORE_CATEGORY_QUESTIONS', 'api/Integration/store/{id}/questions'),

        'store-create' => env('VIKING_API_STORE_CREATE', 'api/Integration/store'),
        'store-detail' => env('VIKING_API_STORE_DETAIL', 'api/Integration/{id}/store'),
        'store-update' => env('VIKING_API_STORE_UPDATE', 'api/Integration/{id}/store'),
        'store-delete' => env('VIKING_API_STORE_DELETE', 'api/Integration/{id}/store'),

        'store-update-categories' => env('VIKING_API_STORE_UPDATE_CATEGORIES', 'api/Integration/store/{id}/categories'),
        'store-add-image' => env('VIKING_API_STORE_ADD_IMAGE', 'api/Integration/store/image'),
        'store-update-image' => env('VIKING_API_STORE_UPDATE_IMAGE', 'api/Integration/store/replace-image'),
        'store-delete-image' => env('VIKING_API_STORE_DELETE_IMAGE', 'api/Integration/store/image'),
        'store-update-schedules' => env('VIKING_API_STORE_UPDATE_SCHEDULES', 'api/Integration/store/{id}/schedules'),
        'store-update-description' => env('VIKING_API_STORE_UPDATE_DESCRIPTION', 'api/Integration/store/{id}/description'),
        'store-update-social' => env('VIKING_API_STORE_UPDATE_SOCIAL', 'api/Integration/store/{id}/social'),
        'store-update-services' => env('VIKING_API_STORE_UPDATE_SERVICES', 'api/Integration/store/{id}/services'),
        'store-update-answers' => env('VIKING_API_STORE_UPDATE_ANSWERS', 'api/Integration/store/{id}/answers'),
        'store-update-subscription' => env('VIKING_API_STORE_UPDATE_SUBSCRIPTION', 'api/Integration/store/{id}/subscription-plan'),

        'store-claim-process' => env('VIKING_API_STORE_CLAIM_PROCESS', '/api/Integration/store/process-claim-business'),

        'file-download' => env('VIKING_API_FILE_DOWNLOAD', '/api/Integration/file/{id}/download'),

        'holiday-event-create' => env('VIKING_API_STORE_HOLIDAY_EVENT_UPDATE', 'api/Integration/HolidayEvent'),
        'holiday-event' => env('VIKING_API_STORE_HOLIDAY_EVENT', 'api/Integration/HolidayEvent/{id}'),
        'holiday-event-get-list' => env('VIKING_API_STORE_HOLIDAY_EVENT_LIST', 'api/Integration/HolidayEvent/get-list'),
        'holiday-event-detail' => env('VIKING_API_STORE_HOLIDAY_EVENT_DETAIL', 'api/Integration/HolidayEvent/{id}'),
        'holiday-event-status-update' => env('VIKING_API_STORE_HOLIDAY_EVENT_STATUS_UPDATE', 'api/Integration/HolidayEvent/{id}/status'),
    ],
    'aes' => [
        'key' => env('VIKING_AES_KEY', 'o5Ec/JKnFlbF6TZB3PjhvPzBHc3pFBWYITAUC0inmnc='),
        'iv' => env('VIKING_AES_IV', '5TYAyJcOxxk2TESw8UayhQ==')
    ],
    'specific_help' => [
        'status' => [
            0 => 'Pending',
            1 => 'In-progress',
            2 => 'Resolved'
        ],
        'mail' => [
            'in_progress' => [
                'subject' => 'Mise à jour de votre demande en cours sur vikîA'
            ],
            'resolved' => [
                'subject' => 'Mise à jour de votre demande sur vikîA'
            ]
        ]
    ]
];
