<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StoreBusinessClaimDocument extends Model
{
    protected $table = 'storebusinessclaimdocuments';
    protected $primaryKey = 'Id';
    public $incrementing = false;
    protected $keyType = 'string';
    public $timestamps = false;

    protected $fillable = [
        'Id',
        'FileDetailId',
        'BusinessClaimId',
        'CreatedBy',
        'CreatedDate',
        'ModifiedBy',
        'ModifiedDate',
        'IsDeleted',
    ];

    protected $casts = [
        'CreatedDate' => 'datetime',
        'ModifiedDate' => 'datetime',
        'IsDeleted' => 'boolean',
    ];

    /**
     * Get the business claim that this document belongs to
     */
    public function businessClaim(): BelongsTo
    {
        return $this->belongsTo(StoreBusinessClaim::class, 'BusinessClaimId', 'Id');
    }

    /**
     * Get the file detail associated with this document
     */
    public function fileDetail(): BelongsTo
    {
        return $this->belongsTo(FileDetail::class, 'FileDetailId', 'Id');
    }
} 