<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClaimActivity extends Model
{
    protected $table = 'claim_activities';
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'claim_id',
        'user_id',
        'action_type',
        'description',
        'metadata',
        'created_at'
    ];

    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the claim that this activity belongs to
     */
    public function claim(): BelongsTo
    {
        return $this->belongsTo(StoreBusinessClaim::class, 'claim_id', 'Id');
    }

    /**
     * Get the user who performed this action
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get action type label
     */
    public function getActionLabel(): string
    {
        $labels = [
            'claim_created' => 'Revendication créée',
            'claim_approved' => 'Revendication approuvée',
            'claim_rejected' => 'Revendication rejetée',
            'info_requested' => 'Informations supplémentaires demandées',
            'document_uploaded' => 'Document téléchargé',
            'status_changed' => 'Statut modifié'
        ];

        return $labels[$this->action_type] ?? 'Action inconnue';
    }

    /**
     * Get formatted timestamp
     */
    public function getFormattedTimestamp(): string
    {
        return $this->created_at->format('d F Y, H:i');
    }
} 