<?php

namespace App\Models;

use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StoreRenewalReminder extends Model
{
    use Filterable;

    protected $table = 'storerenewalreminders';

    public $timestamps = false;

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class, 'SubscriptionId', 'Id');
    }
}
