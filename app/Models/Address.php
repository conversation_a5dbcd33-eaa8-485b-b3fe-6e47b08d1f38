<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Address extends Model
{
    protected $table = 'addresses';

    public $timestamps = false;

    protected $fillable = [
        'AddressLine1',
        'AddressLine2',
        'City',
        'ZipCode',
        'Latitude',
        'Longitude',
        'PhoneNumber',
        'StoreId'
    ];


    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'StoreId', 'Id');
    }

    public function schedule(): HasOne
    {
        return $this->hasOne(AddressSchedule::class, 'AddressId', 'Id');
    }
}
