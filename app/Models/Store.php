<?php

namespace App\Models;

use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class Store extends Model
{
    use Filterable;

    protected $table = 'stores';

    public $timestamps = false;

    protected $fillable = [
        'Status',
        'Name',
        'EmailDecrypted',
        'BusinessId',
        'APECode'
    ];

    public function subscription(): HasOne
    {
        return $this->hasOne(Subscription::class, 'StoreId', 'Id');
    }

    public function address(): HasOne
    {
        return $this->hasOne(Address::class, 'StoreId', 'Id');
    }

    public function business(): HasOne
    {
        return $this->hasOne(Business::class, 'Id', 'BusinessId');
    }

    public function category(): HasOneThrough
    {
        return $this->hasOneThrough(
            Category::class,
            StoreCategory::class,
            'StoreId',
            'Id',
            'Id',
            'CategoryId'
        );
    }

    public function categories(): HasManyThrough
    {
        return $this->hasManyThrough(
            Category::class,
            StoreCategory::class,
            'StoreId',
            'Id',
            'Id',
            'CategoryId'
        );
    }

    public function employee(): HasOneThrough
    {
        return $this->hasOneThrough(
            Employee::class,
            StoreEmployee::class,
            'StoreId',
            'Id',
            'Id',
            'EmployeeId'
        );
    }

    public function schedule(): HasManyThrough
    {
        return $this->hasManyThrough(
            AddressSchedule::class,
            Address::class,
            'StoreId',
            'AddressId',
            'Id',
            'Id'
        );
    }

    public function subscriptionPlan(): HasOneThrough
    {
        return $this->hasOneThrough(
            SubscriptionPlan::class,
            Subscription::class,
            'StoreId',
            'Id',
            'Id',
            'SubscriptionPlanId'
        );
    }

    public function storeRenewalReminder(): HasMany
    {
        return $this->hasMany(StoreRenewalReminder::class, 'StoreId', 'Id');
    }

    public function storeBusinessClaims(): HasMany
    {
        return $this->hasMany(StoreBusinessClaim::class, 'StoreId', 'Id');
    }

    public function getMainCategories()
    {
        return $this->categories->pluck('MainCategoryName')->unique()->filter()->values()->toArray();
    }

    public function filterByName(Builder $query, string $value = null) : Builder
    {
        $keywords = explode(',', $value);
        return $query->where(function ($q) use ($keywords) {
            foreach ($keywords as $keyword) {
                $keyword = trim(urldecode($keyword));
                if ($keyword != null && $keyword != '') {
                    $q->where('Name', 'LIKE', '%' . trim($keyword) . '%');
                }
            }
        });
    }

    public function addressSchedule()
    {
        return $this->address?->schedule;
    }
}
