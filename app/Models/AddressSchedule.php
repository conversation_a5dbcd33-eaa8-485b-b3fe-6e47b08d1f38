<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AddressSchedule extends Model
{
    protected $table = 'addressschedules';

    public $timestamps = false;

    protected $primaryKey = 'Id';

    protected $fillable = [
        'Id',
        'AddressId',
        'DayOfWeek',
        'MorningOpenTime',
        'MorningCloseTime',
        'PostalCode',
        'AfternoonOpenTime',
        'AfternoonCloseTime',
        'FromDate',
        'ToDate',
        'CreatedDate',
        'ModifiedDate',
        'IsDeleted'
    ];

    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'AddressId', 'Id');
    }
}
