<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Import extends Model
{
    protected $table = 'imports';
    protected $casts = [
        'invalid_row' => 'array',
    ];

    const STATUS_IMPORT_FAILED = 'failed';
    const STATUS_IMPORT_SUCCESS = 'success';

    const STATUS_PROCESSING = 'processing';
    const STATUS_FAILED = 'failed';
    const STATUS_COMPLETED = 'completed';

    protected $fillable = [
        'user_id',
        'original_name',
        'status',
        'batch_id',
        'invalid_count',
        'invalid_row',
        'valid_count',
        'valid_file',
        'status_import',
        'message'
    ];
}
