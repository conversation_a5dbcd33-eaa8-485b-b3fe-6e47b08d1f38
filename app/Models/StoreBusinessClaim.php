<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class StoreBusinessClaim extends Model
{
    protected $table = 'storebusinessclaims';
    protected $primaryKey = 'Id';
    public $incrementing = false;
    protected $keyType = 'string';
    public $timestamps = false;

    protected $fillable = [
        'Id',
        'CreatedBy',
        'CreatedDate',
        'ModifiedBy',
        'ModifiedDate',
        'IsDeleted',
        'ClaimBusinessStatus',
        'StoreId',
        'UserId',
        'FileDetailId',
        'RejectedReason',
        'RejectedBy',
        'RejectedDate',
        'RequestInfo',
        'RequestedBy',
        'RequestedDate',
    ];

    protected $casts = [
        'CreatedDate' => 'datetime',
        'ModifiedDate' => 'datetime',
        'IsDeleted' => 'boolean',
        'ClaimBusinessStatus' => 'integer',
        'RejectedDate' => 'datetime',
        'RequestedDate' => 'datetime',
    ];

    /**
     * Get the store that this claim is for
     */
    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'StoreId', 'Id');
    }

    /**
     * Get the user who made this claim
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(UserApp::class, 'UserId', 'Id');
    }

    /**
     * Get the file detail associated with this claim
     */
    public function fileDetail(): BelongsTo
    {
        return $this->belongsTo(FileDetail::class, 'FileDetailId', 'Id');
    }

    /**
     * Get the old documents associated with this claim
     */
    public function oldDocuments(): HasMany
    {
        return $this->hasMany(StoreBusinessClaimDocument::class, 'BusinessClaimId', 'Id');
    }
}
