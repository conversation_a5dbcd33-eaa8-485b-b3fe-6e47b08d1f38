<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    protected $table = 'subscriptions';
    protected $primaryKey = 'Id';

    public $timestamps = false;

    protected $fillable = [
        'Id',
        'StartTime',
        'EndTime',
        'StoreId',
        'SubscriptionPlanId',
        'ModifiedDate',
        'State',
        'CreatedDate',
        'IsDeleted'
    ];
}
