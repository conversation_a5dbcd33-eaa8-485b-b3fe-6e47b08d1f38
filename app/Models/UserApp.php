<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserApp extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'Id';
    public $incrementing = false;
    protected $keyType = 'string';
    public $timestamps = false;

    protected $fillable = [
        'Id',
        'FirstName',
        'LastName',
        'Email',
        'CreatedDate',
        'IsDeleted'
    ];

    protected static function booted()
    {
        static::addGlobalScope('softDelete', function ($query) {
            $query->where('IsDeleted', 0);
        });
    }
}
