<?php

namespace App\Jobs;

use App\Models\Import;
use App\Services\Api\VikingStoreSystemService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use SplFileObject;

class IntegrateValidateStoreJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    protected $importId;

    protected $filePath;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($importId, $filePath)
    {
        $this->importId = $importId;
        $this->filePath = $filePath;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(VikingStoreSystemService $vikingStoreSystemService)
    {
        $invalid = [];
        $path = storage_path('app/public/' . $this->filePath);
        $import = Import::findOrFail($this->importId);

        $body[] = [
            'name' => 'File',
            'contents' => fopen($path, 'r')
        ];

        $response = $vikingStoreSystemService->validate($body);
        if ($response['status'] == ResponseAlias::HTTP_OK) {
            $body = json_decode($response['body']);
            if (isset($body->errors)) {
                $import->update([
                    'status_import' => Import::STATUS_IMPORT_FAILED,
                    'message' => $body->errors[0]->errorMessage
                ]);
            } else {
                if (!$body->success) {
                    $invalid = $body->data->rows;
                    $import->update([
                        'invalid_count' => count($invalid),
                        'invalid_row' => $invalid,
                    ]);
                }

                $invalidRows = array_column($invalid, 'index');
                $validData = buildValidCsv($path, $invalidRows);

                $import->update([
                    'status_import' => Import::STATUS_IMPORT_SUCCESS,
                    'valid_file' => $validData['filename'],
                    'valid_count' => $validData['valid_count']
                ]);
            }
        }
    }
}