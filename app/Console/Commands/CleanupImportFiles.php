<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Models\Import;

class CleanupImportFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:cleanup-import-files';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete valid files in the import storage directory at the end of the day';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting cleanup of import files...');
        
        try {
            // Get all imports with valid_file field that is not null or empty
            $imports = Import::whereNotNull('valid_file')
                ->where('valid_file', '!=', '')
                ->get();
            
            $count = 0;
            $failedCount = 0;
            if ($imports->count() > 0) {
                foreach ($imports as $import) {
                    $filePath = 'import/' . $import->valid_file;
                    
                    // Check if file exists in storage
                    if (Storage::exists($filePath)) {
                        // Delete the file
                        if (Storage::delete($filePath)) {
                            $count++;
                        } else {
                            $failedCount++;
                            Log::warning("CleanupImportFiles: Failed to delete file: {$filePath}");
                        }
                    }
                }
                
                $this->info("Successfully deleted {$count} valid file(s) from storage.");
                
                if ($failedCount > 0) {
                    $this->warn("{$failedCount} file(s) could not be deleted.");
                }
                
                Log::info("CleanupImportFiles: Successfully deleted {$count} valid file(s) from storage. Failed: {$failedCount}");
            } else {
                $this->info('No valid files found to clean up.');
                Log::info('CleanupImportFiles: No valid files found to clean up.');
            }
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Error cleaning up import files: ' . $e->getMessage());
            Log::error('CleanupImportFiles: Error cleaning up import files: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
