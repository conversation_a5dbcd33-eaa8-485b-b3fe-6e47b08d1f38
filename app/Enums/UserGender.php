<?php

namespace App\Enums;

enum UserGender: int {
    case MALE = 0;
    case FEMALE = 1;
    case OTHER = 2;

    public static function asArray(): array
    {
        return [
            null => 'All',
            self::MALE->value => 'Male',
            self::FEMALE->value => 'Female',
            self::OTHER->value => 'Other',
        ];
    }

    public static function getLabel(?int $value): string
    {
        $enumArray = self::asArray();
        return $enumArray[$value] ?? 'Unknown';
    }
}
