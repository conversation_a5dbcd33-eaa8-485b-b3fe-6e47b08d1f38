<?php

namespace App\Enums;

enum StoreBusinessClaimStatus: int {
    case PENDING = 0;
    case APPROVED = 1;
    case REJECTED = 2;
    case INFO_REQUESTED = 3;
    case UNDER_REVIEW = 4;

    public static function asArray(): array
    {
        return [
            self::PENDING->value => 'Pending',
            self::APPROVED->value => 'Approved',
            self::REJECTED->value => 'Rejected',
            self::INFO_REQUESTED->value => 'Info Requested',
            self::UNDER_REVIEW->value => 'Under Review',
        ];
    }
}
