<?php

namespace App\Repositories\Eloquent;

use App\Enums\ScheduleTemplateStatus;
use App\Models\ScheduleTemplate;
use App\Repositories\Contract\ScheduleTemplateRepositoryInterface;

class EloquentScheduleTemplateRepository extends EloquentBaseRepository implements ScheduleTemplateRepositoryInterface
{
    public function model(): string
    {
        return ScheduleTemplate::class;
    }

    public function getAllOrderedByCreatedAt()
    {
        return $this->model->orderBy('created_at', 'desc')->get();
    }

    public function updateStatus(int $id, ScheduleTemplateStatus $status)
    {
        $template = $this->find($id);
        if ($template) {
            return $template->update(['status' => $status]);
        }
        return false;
    }
}
