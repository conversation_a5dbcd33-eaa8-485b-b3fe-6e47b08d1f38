<?php

namespace App\Repositories\Eloquent;

use App\Models\StoreBusinessClaim;
use App\Repositories\Contract\StoreBusinessClaimRepositoryInterface;

class EloquentStoreBusinessClaimRepository extends EloquentBaseRepository implements StoreBusinessClaimRepositoryInterface
{
    public function model(): string
    {
        return StoreBusinessClaim::class;
    }

    public function getQuery()
    {
        return $this->model->query();
    }
}
