<?php

namespace App\Services;

use App\Models\StoreBusinessClaim;
use App\Models\FileDetail;
use App\Models\StoreBusinessClaimDocument;
use App\Enums\StoreBusinessClaimStatus;
use App\Enums\UserGender;
use App\Repositories\Contract\StoreBusinessClaimRepositoryInterface;
use App\Services\Api\VikingStoreSystemService;
use App\Services\ClaimActivityService;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

class StoreBusinessClaimService
{
    private const NOT_AVAILABLE = 'N/A';

    public function __construct(
        protected StoreBusinessClaimRepositoryInterface $storeBusinessClaimRepository,
        protected VikingStoreSystemService $vikingStoreSystemService,
        protected ClaimActivityService $claimActivityService
    )
    {

    }

    /**
     * Get paginated store business claims with filters
     *
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getFilteredData(array $filters = []): LengthAwarePaginator
    {
        $query = $this->storeBusinessClaimRepository->getQuery()
            ->select([
                'storebusinessclaims.Id',
                'storebusinessclaims.CreatedDate',
                'storebusinessclaims.ClaimBusinessStatus',
                'storebusinessclaims.StoreId',
                'storebusinessclaims.UserId',
                'stores.Name as StoreName',
                'users.FirstName',
                'users.LastName'
            ])
            ->leftJoin('stores', 'storebusinessclaims.StoreId', '=', 'stores.Id')
            ->leftJoin('users', 'storebusinessclaims.UserId', '=', 'users.Id')
            ->where('storebusinessclaims.IsDeleted', 0);

        $this->applyFilters($query, $filters);

        $this->applySorting($query, $filters);

        $claims = $query->paginate(10);

        $claims->getCollection()->transform(function ($claim) {
            return $this->transformClaimData($claim);
        });

        if (!empty($filters['search'])) {
            $claims = $this->filterBySearch($claims, $filters['search']);
        }

        return $claims;
    }

    /**
     * Apply filters to the query
     *
     * @param $query
     * @param array $filters
     */
    private function applyFilters($query, array $filters)
    {
        if (!empty($filters['status'])) {
            $query->where('storebusinessclaims.ClaimBusinessStatus', $filters['status']);
        }
    }

    /**
     * Apply sorting to the query
     *
     * @param $query
     * @param array $filters
     */
    private function applySorting($query, array $filters)
    {
        $sortField = $filters['sort_field'] ?? 'created_date';
        $sortDirection = $filters['sort_direction'] ?? 'desc';

        $sortDirection = in_array(strtolower($sortDirection), ['asc', 'desc']) ? strtolower($sortDirection) : 'desc';

        switch ($sortField) {
            case 'created_date':
            case 'date_submitted':
                $query->orderBy('storebusinessclaims.CreatedDate', $sortDirection);
                break;
            case 'store_name':
                $query->orderBy('stores.Name', $sortDirection);
                break;
            case 'status':
                $query->orderBy('storebusinessclaims.ClaimBusinessStatus', $sortDirection);
                break;
            default:
                $query->orderBy('storebusinessclaims.CreatedDate', 'desc');
                break;
        }
    }

    /**
     * Filter claims by combined search (store name or user name) after decryption
     *
     * @param LengthAwarePaginator $claims
     * @param string $searchTerm
     * @return LengthAwarePaginator
     */
    private function filterBySearch(LengthAwarePaginator $claims, string $searchTerm): LengthAwarePaginator
    {
        $filteredItems = $claims->getCollection()->filter(function ($claim) use ($searchTerm) {
            return stripos($claim->StoreName, $searchTerm) !== false ||
                   stripos($claim->OwnerRepresentative, $searchTerm) !== false;
        });

        $currentPage = $claims->currentPage();
        $perPage = $claims->perPage();
        $total = $filteredItems->count();

        return new LengthAwarePaginator(
            $filteredItems->values(),
            $total,
            $perPage,
            $currentPage,
            [
                'path' => request()->url(),
                'pageName' => 'page',
            ]
        );
    }

    /**
     * Transform claim data for display
     *
     * @param $claim
     * @return object
     */
    private function transformClaimData($claim)
    {
        $numericId = crc32($claim->Id) & 0x7FFFFFFF;
        $formattedId = str_pad($numericId % 999999 + 1, 6, '0', STR_PAD_LEFT);

        $ownerRepresentative = $this->getOwnerRepresentativeName($claim);

        return (object) [
            'Id' => $claim->Id,
            'FormattedId' => $formattedId,
            'StoreName' => $claim->StoreName ?? self::NOT_AVAILABLE,
            'OwnerRepresentative' => $ownerRepresentative,
            'DateSubmitted' => $claim->CreatedDate ? Carbon::parse($claim->CreatedDate)->setTimezone('Europe/Paris')->format('d/m/Y') : self::NOT_AVAILABLE,
            'Status' => $this->getStatusLabel($claim->ClaimBusinessStatus),
            'StatusValue' => $claim->ClaimBusinessStatus,
            'StoreId' => $claim->StoreId,
            'UserId' => $claim->UserId
        ];
    }

    /**
     * Get owner/representative name, handling encrypted data
     *
     * @param $claim
     * @return string
     */
    private function getOwnerRepresentativeName($claim): string
    {
        $firstName = $claim->FirstName ?? '';
        $lastName = $claim->LastName ?? '';

        if ($this->isEncryptedData($firstName)) {
            $decryptedFirstName = decryptData($firstName);
            if ($decryptedFirstName !== false) {
                $firstName = $decryptedFirstName;
            }
        }

        if ($this->isEncryptedData($lastName)) {
            $decryptedLastName = decryptData($lastName);
            if ($decryptedLastName !== false) {
                $lastName = $decryptedLastName;
            }
        }

        $fullName = trim($firstName . ' ' . $lastName);
        return $fullName ?: self::NOT_AVAILABLE;
    }

    /**
     * Check if data appears to be encrypted/encoded
     *
     * @param string|null $data
     * @return bool
     */
    private function isEncryptedData(?string $data): bool
    {
        if (empty($data)) {
            return false;
        }

        return (
            strlen($data) > 20 &&
            (preg_match('/^[A-Za-z0-9+\/=]+$/', $data) ||
             preg_match('/[A-Za-z0-9]{20,}/', $data))
        );
    }

    /**
     * Get claim details by ID
     *
     * @param string $id
     * @return object|null
     */
    public function getClaimDetails(string $id)
    {
        $claim = $this->storeBusinessClaimRepository->getQuery()
            ->select([
                'storebusinessclaims.*',
                'stores.Name as StoreName',
                'users.FirstName',
                'users.LastName',
                'users.Email as UserEmail'
            ])
            ->leftJoin('stores', 'storebusinessclaims.StoreId', '=', 'stores.Id')
            ->leftJoin('users', 'storebusinessclaims.UserId', '=', 'users.Id')
            ->where('storebusinessclaims.Id', $id)
            ->where('storebusinessclaims.IsDeleted', 0)
            ->first();

        if (!$claim) {
            return null;
        }

        return $this->transformClaimData($claim);
    }

    /**
     * Get status label based on status value using enum
     *
     * @param int|null $status
     * @return string
     */
    private function getStatusLabel($status)
    {
        $enumArray = StoreBusinessClaimStatus::asArray();
        return $enumArray[$status] ?? 'Unknown';
    }

    /**
     * @param string|null $data
     * @return string|null
     */
    private function decryptClaimData(?string $data): ?string
    {
        if (empty($data)) {
            return null;
        }

        if ($this->isEncryptedData($data)) {
            $decryptedData = decryptData($data);
            if ($decryptedData !== false) {
                return $decryptedData;
            }
        }

        return $data;
    }

    /**
     * Get document URL for download/view
     *
     * @param FileDetail $fileDetail
     * @return string
     */
    private function getDocumentUrl(FileDetail $fileDetail): string
    {
        if ($fileDetail->Link && filter_var($fileDetail->Link, FILTER_VALIDATE_URL)) {
            return $fileDetail->Link;
        }

        if ($fileDetail->Path) {
            return route('store.claims.document.download', $fileDetail->Id);
        }

        return '#';
    }

    /**
     * Get filter options for the form using enum
     *
     * @return array
     */
    public function getFilterOptions(): array
    {
        return [
            'statusOptions' => StoreBusinessClaimStatus::asArray()
        ];
    }

    /**
     * Get detailed claim information for modal display
     *
     * @param StoreBusinessClaim $claim
     * @return object|null
     */
    public function getClaimDetail(StoreBusinessClaim $claim): ?object
    {
        $claim = $this->storeBusinessClaimRepository->getQuery()
            ->select([
                'storebusinessclaims.Id',
                'storebusinessclaims.CreatedDate',
                'storebusinessclaims.ClaimBusinessStatus',
                'storebusinessclaims.StoreId',
                'storebusinessclaims.UserId',
                'storebusinessclaims.FileDetailId',
                'storebusinessclaims.rejectedReason',
                'stores.Name as StoreName',
                'users.FirstName',
                'users.LastName',
                'users.Email',
                'users.Gender',
                'users.AddressLine1',
                'users.City'
            ])
            ->leftJoin('stores', 'storebusinessclaims.StoreId', '=', 'stores.Id')
            ->leftJoin('users', 'storebusinessclaims.UserId', '=', 'users.Id')
            ->where('storebusinessclaims.Id', $claim->Id)
            ->where('storebusinessclaims.IsDeleted', 0)
            ->first();

        if (!$claim) {
            return null;
        }

        return $this->transformClaimDetailData($claim);
    }

    /**
     * Transform claim detail data for modal display
     *
     * @param $claim
     * @return object
     */
    private function transformClaimDetailData($claim): object
    {
        $firstName = $this->decryptClaimData($claim->FirstName);
        $lastName = $this->decryptClaimData($claim->LastName);
        $email = $this->decryptClaimData($claim->Email);
        $address = $this->decryptClaimData($claim->AddressLine1);
        $city = $this->decryptClaimData($claim->City);

        return (object) [
            'id' => $claim->Id,
            'storeName' => $claim->StoreName ?? self::NOT_AVAILABLE,
            'StoreId' => $claim->StoreId,
            'dateSubmitted' => $claim->CreatedDate ? Carbon::parse($claim->CreatedDate)->setTimezone('Europe/Paris')->format('d F Y, H:i') : self::NOT_AVAILABLE,
            'status' => $claim->ClaimBusinessStatus,
            'statusValue' => $claim->ClaimBusinessStatus,
            'rejectReason' => $claim->rejectedReason,
            'user' => (object) [
                'firstName' => $firstName,
                'lastName' => $lastName,
                'gender' => UserGender::getLabel($claim->Gender),
                'address' => $address ?: self::NOT_AVAILABLE,
                'city' => $city ?: self::NOT_AVAILABLE,
                'email' => $email ?: self::NOT_AVAILABLE
            ],
            'documents' => $this->getClaimDocuments($claim->FileDetailId, $claim->Id),
            'activities' => $this->claimActivityService->getFormattedActivities($claim->Id)
        ];
    }

    /**
     * Get claim documents from filedetails table with API integration
     * Includes both current document and old documents
     *
     * @param string|null $fileDetailId
     * @param string $claimId
     * @return array
     */
    private function getClaimDocuments(?string $fileDetailId, string $claimId): array
    {
        $documents = [];

        if ($fileDetailId) {
            $fileDetail = FileDetail::where('Id', $fileDetailId)->where('IsDeleted', 0)->first();

            if ($fileDetail) {
                $documents[] = [
                    'id' => $fileDetail->Id,
                    'filename' => $fileDetail->FileName,
                    'fileType' => $fileDetail->file_extension,
                    'fileSize' => $fileDetail->formatted_file_size,
                    'uploadDate' => $fileDetail->CreatedDate ? Carbon::parse($fileDetail->CreatedDate)->setTimezone('Europe/Paris')->format('d F Y, H:i') : self::NOT_AVAILABLE,
                    'url' => $this->getDocumentUrl($fileDetail),
                    'isImage' => $fileDetail->is_image,
                    'containerName' => $fileDetail->ContainerName,
                    'path' => $fileDetail->Path,
                    'isCurrent' => true
                ];
            }
        }

        $oldDocuments = StoreBusinessClaimDocument::where('BusinessClaimId', $claimId)
            ->where('IsDeleted', 0)
            ->with('fileDetail')
            ->orderBy('CreatedDate', 'desc')
            ->get();

        foreach ($oldDocuments as $oldDoc) {
            if ($oldDoc->fileDetail && $oldDoc->fileDetail->IsDeleted == 0) {
                $documents[] = [
                    'id' => $oldDoc->fileDetail->Id,
                    'filename' => $oldDoc->fileDetail->FileName,
                    'fileType' => $oldDoc->fileDetail->file_extension,
                    'fileSize' => $oldDoc->fileDetail->formatted_file_size,
                    'uploadDate' => $oldDoc->fileDetail->CreatedDate ? Carbon::parse($oldDoc->fileDetail->CreatedDate)->setTimezone('Europe/Paris')->format('d F Y, H:i') : self::NOT_AVAILABLE,
                    'url' => $this->getDocumentUrl($oldDoc->fileDetail),
                    'isImage' => $oldDoc->fileDetail->is_image,
                    'containerName' => $oldDoc->fileDetail->ContainerName,
                    'path' => $oldDoc->fileDetail->Path,
                    'isCurrent' => false,
                    'oldDocumentId' => $oldDoc->Id
                ];
            }
        }

        return $documents;
    }

    /**
     * Approve a store business claim
     *
     * @param StoreBusinessClaim $claim
     * @return bool
     */
    public function approveClaim(StoreBusinessClaim $claim): bool
    {
        try {
            $oldStatus = $claim->ClaimBusinessStatus;
            $updateData = [
                'ClaimBusinessStatus' => StoreBusinessClaimStatus::APPROVED->value
            ];

            $updatedClaim = $this->storeBusinessClaimRepository->update($updateData, $claim->Id);

            if ($updatedClaim) {
                $this->vikingStoreSystemService->processStoreClaim($claim->Id, StoreBusinessClaimStatus::APPROVED->value);

                $this->claimActivityService->logClaimApproved($updatedClaim);

                if ($oldStatus !== StoreBusinessClaimStatus::APPROVED->value) {
                    $this->claimActivityService->logStatusChanged($updatedClaim, $oldStatus, StoreBusinessClaimStatus::APPROVED->value);
                }

                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Failed to approve claim: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Reject a store business claim
     *
     * @param StoreBusinessClaim $claim
     * @param array $data
     * @return bool
     */
    public function rejectClaim(StoreBusinessClaim $claim, array $data): bool
    {
        try {
            $oldStatus = $claim->ClaimBusinessStatus;
            $updateData = [
                'ClaimBusinessStatus' => StoreBusinessClaimStatus::REJECTED->value,
                'RejectedReason' => $data['reject_reason'],
                'RejectedBy' => auth()->id(),
                'RejectedDate' => now()
            ];

            $updatedClaim = $this->storeBusinessClaimRepository->update($updateData, $claim->Id);

            if ($updatedClaim) {
                $this->vikingStoreSystemService->processStoreClaim($claim->Id, StoreBusinessClaimStatus::REJECTED->value);

                $this->claimActivityService->logClaimRejected($updatedClaim);

                if ($oldStatus !== StoreBusinessClaimStatus::REJECTED->value) {
                    $this->claimActivityService->logStatusChanged($updatedClaim, $oldStatus, StoreBusinessClaimStatus::REJECTED->value);
                }

                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Failed to reject claim: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Request additional information for a store business claim
     *
     * @param StoreBusinessClaim $claim
     * @param array $data
     * @return bool
     */
    public function requestClaimInfo(StoreBusinessClaim $claim, array $data): bool
    {
        try {
            $oldStatus = $claim->ClaimBusinessStatus;
            $updateData = [
                'ClaimBusinessStatus' => StoreBusinessClaimStatus::INFO_REQUESTED->value,
                'RequestInfo' => $data['request_info'],
                'RequestedBy' => auth()->id(),
                'RequestedDate' => now()
            ];

            $updatedClaim = $this->storeBusinessClaimRepository->update($updateData, $claim->Id);

            if ($updatedClaim) {
                $this->vikingStoreSystemService->processStoreClaim($claim->Id, StoreBusinessClaimStatus::INFO_REQUESTED->value);

                $this->claimActivityService->logInfoRequested($updatedClaim, $data['request_info']);

                if ($oldStatus !== StoreBusinessClaimStatus::INFO_REQUESTED->value) {
                    $this->claimActivityService->logStatusChanged($updatedClaim, $oldStatus, StoreBusinessClaimStatus::INFO_REQUESTED->value);
                }

                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Failed to request claim info: ' . $e->getMessage());
            return false;
        }
    }
}
