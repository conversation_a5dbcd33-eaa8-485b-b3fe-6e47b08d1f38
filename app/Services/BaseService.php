<?php

namespace App\Services;

use App\Enums\SubscriptionLevel;
use App\Enums\SubscriptionType;
use Illuminate\Database\Eloquent\Builder;

class BaseService
{
    public function applyTypeFilter(Builder $query, ?string $type): void
    {
        if ($type === SubscriptionType::MEMBERSHIP->value) {
            $query->whereHas('subscriptionPlan', function($q) {
                $q->where('PlanType', '!=', SubscriptionLevel::BASIC_TYPE->value);
            });
        } elseif ($type === SubscriptionType::NON_MEMBERSHIP->value) {
            $query->where(function($query) {
                $query->whereHas('subscriptionPlan', function($q) {
                    $q->where('PlanType', SubscriptionLevel::BASIC_TYPE->value);
                })->orWhereDoesntHave('subscriptionPlan');
            });
        }
    }
}
