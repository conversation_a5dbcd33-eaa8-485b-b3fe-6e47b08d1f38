<?php

namespace App\Services;

use App\Models\ClaimActivity;
use App\Models\StoreBusinessClaim;
use App\Enums\StoreBusinessClaimStatus;
use Illuminate\Database\Eloquent\Collection;

class ClaimActivityService
{
    /**
     * Log a claim activity
     *
     * @param string $claimId
     * @param string $actionType
     * @param string $description
     * @param array|null $metadata
     * @return ClaimActivity
     */
    public function logActivity(string $claimId, string $actionType, string $description, ?array $metadata = null): ClaimActivity
    {
        return ClaimActivity::create([
            'claim_id' => $claimId,
            'user_id' => auth()->id(),
            'action_type' => $actionType,
            'description' => $description,
            'metadata' => $metadata,
            'created_at' => now()->setTimezone('Europe/Paris')
        ]);
    }

    /**
     * Log claim approval activity
     *
     * @param StoreBusinessClaim $claim
     * @return ClaimActivity
     */
    public function logClaimApproved(StoreBusinessClaim $claim): ClaimActivity
    {
        return $this->logActivity(
            $claim->Id,
            'claim_approved',
            'Vous avez approuvé cette revendication',
            [
                'store_id' => $claim->StoreId,
                'approved_by' => auth()->id(),
                'approved_at' => now()->setTimezone('Europe/Paris')->toISOString()
            ]
        );
    }

    /**
     * Log claim rejection activity
     *
     * @param StoreBusinessClaim $claim
     * @return ClaimActivity
     */
    public function logClaimRejected(StoreBusinessClaim $claim): ClaimActivity
    {
        return $this->logActivity(
            $claim->Id,
            'claim_rejected',
            'Vous avez rejeté cette revendication',
            [
                'store_id' => $claim->StoreId,
                'rejected_by' => auth()->id(),
                'rejected_at' => now()->setTimezone('Europe/Paris')->toISOString()
            ]
        );
    }

    /**
     * Log info request activity
     *
     * @param StoreBusinessClaim $claim
     * @param string $requestInfo
     * @return ClaimActivity
     */
    public function logInfoRequested(StoreBusinessClaim $claim, string $requestInfo): ClaimActivity
    {
        return $this->logActivity(
            $claim->Id,
            'info_requested',
            'Vous avez demandé des informations supplémentaires',
            [
                'store_id' => $claim->StoreId,
                'requested_by' => auth()->id(),
                'requested_at' => now()->setTimezone('Europe/Paris')->toISOString(),
                'request_info' => $requestInfo
            ]
        );
    }

    /**
     * Log status change activity
     *
     * @param StoreBusinessClaim $claim
     * @param int $oldStatus
     * @param int $newStatus
     * @return ClaimActivity
     */
    public function logStatusChanged(StoreBusinessClaim $claim, int $oldStatus, int $newStatus): ClaimActivity
    {
        $oldStatusLabel = StoreBusinessClaimStatus::from($oldStatus)->name;
        $newStatusLabel = StoreBusinessClaimStatus::from($newStatus)->name;

        return $this->logActivity(
            $claim->Id,
            'status_changed',
            "Statut modifié de {$oldStatusLabel} vers {$newStatusLabel}",
            [
                'store_id' => $claim->StoreId,
                'changed_by' => auth()->id(),
                'changed_at' => now()->setTimezone('Europe/Paris')->toISOString(),
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'old_status_label' => $oldStatusLabel,
                'new_status_label' => $newStatusLabel
            ]
        );
    }

    /**
     * Get activities for a specific claim
     *
     * @param string $claimId
     * @return Collection
     */
    public function getClaimActivities(string $claimId): Collection
    {
        return ClaimActivity::where('claim_id', $claimId)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get formatted activities for display
     *
     * @param string $claimId
     * @return array
     */
    public function getFormattedActivities(string $claimId): array
    {
        $activities = $this->getClaimActivities($claimId);

        return $activities->map(function ($activity) {
            return [
                'id' => $activity->id,
                'timestamp' => $activity->getFormattedTimestamp(),
                'action_label' => $activity->getActionLabel(),
                'description' => $activity->description,
                'metadata' => $activity->metadata,
                'user_name' => $activity->user ? $activity->user->name : 'Système',
                'action_type' => $activity->action_type
            ];
        })->toArray();
    }
}
