<?php

namespace App\Services;

use App\Enums\Status;
use App\Jobs\IntegrateSendMailJob;
use App\Repositories\Contract\SpecificHelpRepositoryInterface;

class SpecificHelpService
{
    protected SpecificHelpRepositoryInterface $specificHelpRepository;

    public function __construct(SpecificHelpRepositoryInterface $specificHelpRepository)
    {
        $this->specificHelpRepository = $specificHelpRepository;
    }

    public function getData()
    {
        $data =  $this->specificHelpRepository->with(['user'])->orderBy('CreatedDate', 'DESC')->all();

        foreach($data->items() as $item) {
            $email = $item->user->Email;
            $item->EmailDecrypted = decryptData($email);
        }

        return $data;
    }

    public function changeStatus(string $id, array $data)
    {
        $specificHelp = $this->specificHelpRepository->with('user')->find($id);
        $info = [
            'emailTo' => decryptData($specificHelp->user->Email),
            'username' => decryptData($specificHelp->user->UserName),
            'description' => $specificHelp->Description,
            'recap' => $data['recap'] ?? null
        ];

        switch ($data['status']) {
            case Status::IN_PROGRESS->value:
                $info['subject'] = config('vikingapp.specific_help.mail.in_progress.subject');
                $info['body'] = view('emails.in-progress-status', ['info' => $info])->render();
                break;
            case Status::RESOLVED->value:
                $info['subject'] = config('vikingapp.specific_help.mail.resolved.subject');
                $info['body'] = view('emails.resolved-status', ['info' => $info])->render();
                break;
        }

        $this->specificHelpRepository->where('Id', $id)
            ->update(['Status' => $data['status']]);

        IntegrateSendMailJob::dispatch($info);

        return ['message' => 'Le Statut a bien été modifié'];
    }

    public function reply(string $id, array $data)
    {
        $specificHelp = $this->specificHelpRepository->with('user')->find($id);
        $info = [
            'emailTo' => decryptData($specificHelp->user->Email),
            'username' => decryptData($specificHelp->user->UserName),
            'body' => $data['body'],
            'subject' => $data['subject']
        ];

        IntegrateSendMailJob::dispatch($info);

        return ['message' => 'La Réponse a bien été envoyée'];
    }
}
