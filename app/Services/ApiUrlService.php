<?php
namespace App\Services;

class ApiUrlService
{
    /**
     * Build API URL from config with dynamic ID replacement
     *
     * @param string $endpoint The endpoint key from config/vikingapp.php
     * @param int|null $id The ID to replace {id} placeholder
     * @return string
     */
    public static function build($endpoint, $id = null)
    {
        $domain = config('vikingapp.api.domain');
        $path = config("vikingapp.api.$endpoint");

        if (!$path) {
            throw new \InvalidArgumentException("API endpoint '$endpoint' not found in configuration");
        }

        $url = rtrim($domain, '/') . '/' . ltrim($path, '/');

        if ($id && strpos($url, '{id}') !== false) {
            $url = str_replace('{id}', $id, $url);
        }

        return $url;
    }

    public static function getStoreInfo($id)
    {
        return self::build('store-info', $id);
    }

    public static function getStoreDetail($id)
    {
        return self::build('store-detail', $id);
    }

    public static function getStoreMasterData()
    {
        return self::build('store-master-data');
    }

    public static function getStoreUpdate($id)
    {
        return self::build('store-update', $id);
    }

    public static function getStoreUpdateCategories($id)
    {
        return self::build('store-update-categories', $id);
    }

    public static function getStoreUpdateImage()
    {
        return self::build('store-update-image');
    }

    public static function getStoreUpdateSocial($id)
    {
        return self::build('store-update-social', $id);
    }

    public static function getStoreUpdateSchedules($id)
    {
        return self::build('store-update-schedules', $id);
    }

    public static function getStoreUpdateDescription($id)
    {
        return self::build('store-update-description', $id);
    }

    public static function getStoreUpdateServices($id)
    {
        return self::build('store-update-services', $id);
    }

    public static function getStoreUpdateAnswers($id)
    {
        return self::build('store-update-answers', $id);
    }

    public static function getStoreUpdateSubscription($id)
    {
        return self::build('store-update-subscription', $id);
    }

    public static function getStoreDelete($id)
    {
        return self::build('store-delete', $id);
    }

    public static function getFileDownload($id)
    {
        return self::build('file-download', $id);
    }
}
