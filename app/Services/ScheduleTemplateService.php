<?php

namespace App\Services;

use App\Enums\ScheduleTemplateStatus;
use App\Exceptions\ScheduleTemplate\MaximumTemplatesExceededException;
use App\Exceptions\ScheduleTemplate\TemplateNotFoundException;
use App\Repositories\Contract\ScheduleTemplateRepositoryInterface;
use Exception;
use Illuminate\Support\Facades\Log;

class ScheduleTemplateService
{
    const MAX_TEMPLATES = 10;

    public function __construct(protected ScheduleTemplateRepositoryInterface $scheduleTemplateRepository)
    {
    }

    /**
     * @throws Exception
     */
    public function getAllTemplates()
    {
        try {
            return $this->scheduleTemplateRepository->getAllOrderedByCreatedAt();
        } catch (Exception $e) {
            Log::error('Failed to get schedule templates: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * @throws MaximumTemplatesExceededException
     * @throws Exception
     */
    public function createTemplate(array $data)
    {
        try {
            $totalTemplates = $this->scheduleTemplateRepository->count();
            if ($totalTemplates >= self::MAX_TEMPLATES) {
                throw new MaximumTemplatesExceededException();
            }

            if (!isset($data['status'])) {
                $data['status'] = ScheduleTemplateStatus::ACTIVE;
            }

            return $this->scheduleTemplateRepository->create($data);
        } catch (MaximumTemplatesExceededException $e) {
            throw $e;
        } catch (Exception $e) {
            Log::error('Failed to create schedule template: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * @throws TemplateNotFoundException
     * @throws Exception
     */
    public function getTemplate(int $id)
    {
        try {
            $template = $this->scheduleTemplateRepository->find($id);

            if (!$template) {
                throw new TemplateNotFoundException();
            }

            return $template;
        } catch (TemplateNotFoundException $e) {
            throw $e;
        } catch (Exception $e) {
            Log::error('Failed to get schedule template: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * @throws TemplateNotFoundException
     * @throws Exception
     */
    public function updateTemplate(int $id, array $data)
    {
        try {
            $template = $this->scheduleTemplateRepository->find($id);

            if (!$template) {
                throw new TemplateNotFoundException();
            }

            return $this->scheduleTemplateRepository->update($data, $id);
        } catch (TemplateNotFoundException $e) {
            throw $e;
        } catch (Exception $e) {
            Log::error('Failed to update schedule template: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * @throws TemplateNotFoundException
     * @throws Exception
     */
    public function deleteTemplate(int $id)
    {
        try {
            $template = $this->scheduleTemplateRepository->find($id);

            if (!$template) {
                throw new TemplateNotFoundException();
            }

            return $this->scheduleTemplateRepository->destroy($id);
        } catch (TemplateNotFoundException $e) {
            throw $e;
        } catch (Exception $e) {
            Log::error('Failed to delete schedule template: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * @throws TemplateNotFoundException
     * @throws Exception
     */
    public function deactivateTemplate(int $id)
    {
        try {
            $template = $this->scheduleTemplateRepository->find($id);

            if (!$template) {
                throw new TemplateNotFoundException();
            }

            return $this->scheduleTemplateRepository->updateStatus($id, ScheduleTemplateStatus::INACTIVE);
        } catch (TemplateNotFoundException $e) {
            throw $e;
        } catch (Exception $e) {
            Log::error('Failed to deactivate schedule template: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * @throws TemplateNotFoundException
     * @throws Exception
     */
    public function reactivateTemplate(int $id)
    {
        try {
            $template = $this->scheduleTemplateRepository->find($id);

            if (!$template) {
                throw new TemplateNotFoundException();
            }

            return $this->scheduleTemplateRepository->updateStatus($id, ScheduleTemplateStatus::ACTIVE);
        } catch (TemplateNotFoundException $e) {
            throw $e;
        } catch (Exception $e) {
            Log::error('Failed to reactivate schedule template: ' . $e->getMessage());
            throw $e;
        }
    }
}
