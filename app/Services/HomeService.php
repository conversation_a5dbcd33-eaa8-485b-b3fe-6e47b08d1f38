<?php

namespace App\Services;

use App\Enums\AppType;
use App\Enums\ChartType;
use App\Repositories\Contract\UserAppRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class HomeService
{
    protected UserAppRepositoryInterface $userAppRepository;
    protected StoreService $storeService;

    public function __construct(
        UserAppRepositoryInterface $userAppRepository,
        StoreService $storeService
    ) {
        $this->userAppRepository = $userAppRepository;
        $this->storeService = $storeService;
    }

    public function getDataHomepage(): array
    {
        $last30date = Carbon::now()->subDays(30)->toDateString();
        $conditionsNewSignup = [
            [DB::raw('DATE(FirstLoginTime)'), '>=', $last30date]
        ];
        $conditionsActive = [
            [DB::raw('DATE(LastLoginTime)'), '>=', $last30date]
        ];
        $totalUserVikingApp = $this->userAppRepository->getTotalUserByApp(AppType::VIKING);
        $totalUserProApp = $this->userAppRepository->getTotalUserByApp(AppType::ADMIN);
        $totalUserNewVikingApp = $this->userAppRepository->getTotalUserByApp(AppType::VIKING, $conditionsNewSignup);
        $totalUserActiveVikingApp = $this->userAppRepository->getTotalUserByApp(AppType::VIKING, $conditionsActive);
        $totalStoresWithSubscription = $this->storeService->getTotalStoresWithSubscription();

        return [
            'totalUserVikingApp' => $totalUserVikingApp ? $totalUserVikingApp->total : 0,
            'totalUserProApp' => $totalUserProApp ? $totalUserProApp->total : 0,
            'totalUserNewVikingApp' => $totalUserNewVikingApp ? $totalUserNewVikingApp->total : 0,
            'totalUserActiveVikingApp' => $totalUserActiveVikingApp ? $totalUserActiveVikingApp->total : 0,
            'totalStoresWithSubscription' => $totalStoresWithSubscription,
        ];
    }

    public function getChart(array $params): array
    {
        list($dateFrom, $dateTo, $conditions) = $this->setupDateFilterConditions($params);

        $dateFrom = Carbon::createFromFormat('Y-m-d', $dateFrom);
        $dateTo = Carbon::createFromFormat('Y-m-d', $dateTo);

        $item = [];
        switch ($params['type']) {
            case ChartType::DAY->value:
                $data = $this->userAppRepository->getTotalUserAppByDay(AppType::VIKING, $conditions);
                $totalUserData = $data->pluck('total', 'date')->toArray();
                for ($date = clone $dateFrom; $date->lte($dateTo); $date->addDay()) {
                    $formatDate = $date->format('Y-m-d');
                    if (in_array($formatDate, array_keys($totalUserData))) {
                        $item[$formatDate] = $totalUserData[$formatDate];

                    } else {
                        $item[$formatDate] = 0;
                    }
                }
                break;
            case ChartType::MONTH->value:
                $data = $this->userAppRepository->getTotalUserAppByMonth(AppType::VIKING, $conditions);
                for ($date = clone $dateFrom; $date->lte($dateTo); $date->addMonth()) {
                    $formatDateFrom = $date->startOfMonth()->format('Y-m-d');
                    $formatYearMonthFrom = $date->format('Y-m');
                    $totalUserData = 0;
                    for ($i = 0; $i < count($data); $i++) {
                        $getMonthDateParse = $data[$i]->year . '-' . $data[$i]->month;
                        if ($formatYearMonthFrom == $getMonthDateParse) {
                            $totalUserData = $data[$i]->total;
                        }
                    }
                    $item[$formatDateFrom] = $totalUserData;
                }
                break;
        }

        return $item;
    }

    public function setupDateFilterConditions($params)
    {
        $type = $params['type'];

        switch ($type) {
            case ChartType::DAY->value:
                $dateFrom = Carbon::now()->subDays(30)->toDateString();
                $dateTo = Carbon::now()->toDateString();
                $conditions = [
                    [DB::raw('DATE(CreatedDate)'), '>=', $dateFrom],
                    [DB::raw('DATE(CreatedDate)'), '<=', $dateTo]
                ];
                break;
            case ChartType::MONTH->value:
                $dateFrom = Carbon::now()->subMonths(23)->toDateString();
                $dateTo = Carbon::now()->toDateString();
                $conditions = [
                    [DB::raw('DATE(CreatedDate)'), '>=', $dateFrom],
                    [DB::raw('DATE(CreatedDate)'), '<=', $dateTo]
                ];
                break;
        }

        return [$dateFrom, $dateTo, $conditions];
    }
}
