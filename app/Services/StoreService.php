<?php

namespace App\Services;

use App\Enums\Status;
use App\Enums\SubscriptionType;
use App\Enums\SubscriptionLevel;
use App\Jobs\IntegrateImportStoreJob;
use App\Models\AddressSchedule;
use App\Models\Category;
use App\Models\ScheduleTemplate;
use App\Models\Store;
use App\Models\StoreCategory;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Repositories\Contract\CityRepositoryInterface;
use App\Services\Api\VikingStoreSystemService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\Import;
use Ramsey\Uuid\Uuid;
use SplFileObject;
use Illuminate\Support\Facades\Bus;
use App\Enums\StoreStatus;
use App\Repositories\Contract\StoreRepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;
use App\Jobs\IntegrateValidateStoreJob;

/**
 * StoreService
 *
 * Service class for handling store-related business logic.
 */
class StoreService
{
    const CHUNK_SIZE = 2;
    protected string $folder = 'import';
    protected BaseService $baseService;
    protected VikingStoreSystemService $vikingStoreSystemService;
    protected StoreRepositoryInterface $storeRepository;
    protected CityRepositoryInterface $cityRepository;

    public function __construct(
        BaseService $baseService,
        VikingStoreSystemService $vikingStoreSystemService,
        StoreRepositoryInterface $storeRepository,
        CityRepositoryInterface $cityRepository
    )
    {
        $this->baseService = $baseService;
        $this->vikingStoreSystemService = $vikingStoreSystemService;
        $this->storeRepository = $storeRepository;
        $this->cityRepository = $cityRepository;
    }

    public function getData(string $type = null, array $filters = []): LengthAwarePaginator
    {
        $query = $this->storeRepository->getQuery()
            ->with(['subscription', 'subscriptionPlan', 'address', 'employee', 'business', 'categories'])
            ->where('IsDeleted', StoreStatus::ACTIVE->value);

        $this->baseService->applyTypeFilter($query, $type);

        $this->applyFormFilters($query, $filters);

        $this->applySorting($query, $filters);

        $stores = $query->paginate(10);

        $stores->getCollection()->transform(function ($store) {
            $store->main_categories = $store->getMainCategories();
            return $store;
        });

        return $stores;
    }

    /**
     * Apply form filters to the query.
     *
     * @param Builder $query
     * @param array $filters
     * @return void
     */
    private function applyFormFilters(Builder $query, array $filters): void
    {
        if (!empty($filters['name_or_email'])) {
            $searchTerm = $filters['name_or_email'];
            $query->where(function($q) use ($searchTerm) {
                $q->where('Name', 'like', '%' . $searchTerm . '%')
                  ->orWhereHas('employee', function($subQ) use ($searchTerm) {
                      $subQ->where('Email', 'like', '%' . $searchTerm . '%');
                  });
            });
        }

        if (!empty($filters['city'])) {
            $query->whereHas('address', function($q) use ($filters) {
                $q->whereRaw('UPPER(City) LIKE ?', ['%' . strtoupper($filters['city']) . '%']);
            });
        }

        if (!empty($filters['subscription_plan'])) {
            $query->whereHas('subscriptionPlan', function($q) use ($filters) {
                $q->where('Name', $filters['subscription_plan']);
            });
        }

        if (!empty($filters['category'])) {
            $query->whereHas('categories', function($q) use ($filters) {
                $q->where('MainCategoryName', $filters['category']);
            });
        }

        if (isset($filters['status']) && $filters['status'] !== '') {
            $query->where('Status', (int) $filters['status']);
        }
    }

    /**
     * Apply sorting to the query
     *
     * @param Builder $query
     * @param array $filters
     * @return void
     */
    private function applySorting(Builder $query, array $filters): void
    {
        $sortField = $filters['sort_field'] ?? 'created_date';
        $sortDirection = $filters['sort_direction'] ?? 'desc';

        $sortDirection = in_array(strtolower($sortDirection), ['asc', 'desc']) ? strtolower($sortDirection) : 'desc';

        switch ($sortField) {
            case 'created_date':
                $query->orderBy('stores.CreatedDate', $sortDirection);
                break;
            case 'name':
                $query->orderBy('stores.Name', $sortDirection);
                break;
            case 'city':
                if (!$this->hasJoin($query, 'addresses')) {
                    $query->leftJoin('addresses', 'stores.Id', '=', 'addresses.StoreId');
                }
                $query->orderBy('addresses.City', $sortDirection);
                break;
            case 'status':
                $query->orderBy('stores.Status', $sortDirection);
                break;
            case 'subscription_plan':
                if (!$this->hasJoin($query, 'subscriptionplans')) {
                    $query->leftJoin('subscriptionplans', 'stores.SubscriptionPlanId', '=', 'subscriptionplans.Id');
                }
                $query->orderBy('subscriptionplans.Name', $sortDirection);
                break;
            default:
                $query->orderBy('stores.CreatedDate', 'desc');
                break;
        }
    }

    /**
     * Check if query already has a specific join
     *
     * @param Builder $query
     * @param string $table
     * @return bool
     */
    private function hasJoin(Builder $query, string $table): bool
    {
        $joins = $query->getQuery()->joins ?? [];
        foreach ($joins as $join) {
            if ($join->table === $table) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get unique values from a relationship field.
     *
     * @param string $relation
     * @param string $field
     * @return array
     */
    public function getUniqueValues(string $relation, string $field): array
    {
        return $this->storeRepository->getQuery()
            ->with($relation)
            ->get()
            ->pluck("$relation.$field")
            ->unique()
            ->filter()
            ->values()
            ->toArray();
    }

    /**
     * Update the status of a store.
     *
     * @param string $id The store ID
     * @param int|StoreStatus $status
     * @return bool
     */
    public function updateStatus(string $id, int|StoreStatus $status): bool
    {
        try {
            $statusValue = $status instanceof StoreStatus ? $status->value : $status;

            $this->storeRepository->where('Id', $id)
                ->update(['Status' => $statusValue]);
            return true;
        } catch (\Exception $e) {
            Log::error('Error updating store status: ' . $e->getMessage());
            return false;
        }
    }

    public function validateStore($file)
    {
        $fileName = uniqueFileName($file->getClientOriginalName(), $this->folder);
        Storage::putFileAs($this->folder, $file, $fileName);

        $import = $this->processFileImport($fileName, IntegrateValidateStoreJob::class);

        return [
            'message' => 'Store data validated successfully!',
            'data' => $import
        ];
    }

    public function import($filename)
    {
        $import =  $this->processFileImport($filename, IntegrateImportStoreJob::class);

        return [
            'message' => 'Store data imported successfully!',
            'data' => $import
        ];
    }

    protected function processFileImport($filename, $jobClass)
    {
        $user = Auth::user();
        $filePath = $this->folder . '/' . $filename;

        $import = Import::create([
            'user_id' => $user->id,
            'original_name' => $filename,
        ]);

        $batch = Bus::batch([new $jobClass($import->id, $filePath)])
            ->name("Import #{$import->id}")
            ->then(function () use ($import) {
                $import->status = Import::STATUS_COMPLETED;
                $import->save();
            })
            ->catch(function () use ($import) {
                $import->status = Import::STATUS_FAILED;
                $import->save();
            })
            ->finally(function () use ($filePath, $import) {
                if ($import->invalid_count > 0 || !$import->valid_file) {
                    Storage::delete($filePath);
                }
            })
            ->dispatch();

        $import->update(['batch_id' => $batch->id]);

        return $import;
    }

    public function processImport($import)
    {
        if ($import->status === Import::STATUS_PROCESSING) {
            $message = "Importing is processing";
        }
        elseif ($import->status === Import::STATUS_FAILED) {
            $message = "Importing is failed";
        } else {
            if ($import->invalid_row) {
                $listRowErrors = array_column($import->invalid_row, 'index');
                $showRowErrors = implode(', ', $listRowErrors);
                $message = trans('response.import.success_with_record_fail') . ' ' . $showRowErrors;
            } else {
                $message = trans('response.import.success');
            }
        }

        return [
            'message' => $message,
            'data' => [
                'user_id' => $import->user_id,
                'status' => $import->status,
                'original_name' => $import->original_name,
                'invalid_count' => $import->invalid_count,
                'invalid_row' => $import->invalid_row,
                'created_at' => $import->created_at
            ]
        ];
    }

    public function getImportByBatchId($batchId): ?Import
    {
        return Import::where('batch_id', $batchId)->first();
    }

    public function getFilterOptions(): array
    {
        $allCategories = $this->getAllAvailableCategories();

        return [
            'categories' => $allCategories,
            'cities' => $this->getUniqueCities(),
            'subscriptionPlans' => $this->getSubscriptionPlans()
        ];
    }

    /**
     * Get all available categories from translation files
     *
     * @return array
     */
    private function getAllAvailableCategories(): array
    {
        $mainCategories = array_keys(__('categories.main_categories'));

        $categories = collect($mainCategories)
            ->filter(function($category) {
                $translated = __("categories.main_categories.{$category}");
                return $translated !== "categories.main_categories.{$category}" && !empty($translated);
            })
            ->sort(function($a, $b) {
                $translatedA = __("categories.main_categories.{$a}");
                $translatedB = __("categories.main_categories.{$b}");
                return strcasecmp($translatedA, $translatedB);
            })
            ->values()
            ->toArray();

        return $categories;
    }

    /**
     * Get unique cities with uppercase normalization.
     *
     * @return array
     */
    public function getUniqueCities(): array
    {
        return $this->storeRepository->getQuery()
            ->with('address')
            ->get()
            ->pluck('address.City')
            ->filter()
            ->map(function($city) {
                return strtoupper($city);
            })
            ->unique()
            ->sort()
            ->values()
            ->toArray();
    }

    public function getCities(): array
    {
        return $this->cityRepository->getQuery()
            ->get()
            ->map(function($city) {
                $cityName = is_object($city) ? ($city->City ?? '') : (string)$city;
                return strtoupper($cityName);
            })
            ->filter()
            ->sort()
            ->values()
            ->toArray();
    }

    public function getFilteredData(?string $type, array $filters, $request): LengthAwarePaginator
    {
        $data = $this->getData($type, $filters);
        $data->appends($request->all());
        return $data;
    }

    public function listSubscription(array $filter)
    {
        $query = $this->storeRepository
            ->with(['subscription' => function ($query) {
                $query->selectRaw("
                    StoreId,
                    DATE(StartTime) as StartTime,
                    DATE(EndTime) as EndTime,
                    CASE
                        WHEN NOW() > EndTime
                          THEN 'Expired'
                        ELSE DATEDIFF(EndTime, NOW())
                    END AS days_left
                    ");
            }, 'subscriptionPlan', 'storeRenewalReminder', 'address'])
            ->filter($filter);

        $perPage = $filter['per_page'] ?? config('pagination.per_page');
        $page = $filter['page'] ?? config('pagination.default_page');
        $paginated = $query->paginate($perPage, ['*'], 'page', $page);
        $paginated->appends($filter);

        return $paginated;
    }

    public function listSubscriptionWithFilters(array $filter)
    {
        $query = $this->storeRepository->getQuery()
            ->with(['subscription' => function ($query) {
                $query->selectRaw("
                    StoreId,
                    DATE(StartTime) as StartTime,
                    DATE(EndTime) as EndTime,
                    CASE
                        WHEN NOW() > EndTime
                          THEN 'Expired'
                        ELSE DATEDIFF(EndTime, NOW())
                    END AS days_left
                    ");
            }, 'subscriptionPlan', 'storeRenewalReminder', 'address']);

        if (!empty($filter['search'])) {
            $searchTerm = $filter['search'];
            $query->where('Name', 'like', '%' . $searchTerm . '%');
        }

        if (isset($filter['status']) && $filter['status'] !== '') {
            $query->where('Status', (int) $filter['status']);
        }

        if (!empty($filter['plan'])) {
            $query->whereHas('subscriptionPlan', function($q) use ($filter) {
                if ($filter['plan'] === 'No Plan') {
                    $q->where('PlanType', SubscriptionLevel::BASIC_TYPE->value);
                } else {
                    $q->where('Name', $filter['plan']);
                }
            });
        }

        if (!empty($filter['sort_by'])) {
            $sortDirection = $filter['sort_direction'] ?? 'asc';

            switch ($filter['sort_by']) {
                case 'status':
                    $query->orderBy('stores.Status', $sortDirection);
                    break;
                case 'plan':
                    $query->leftJoin('subscriptions', 'stores.Id', '=', 'subscriptions.StoreId')
                          ->leftJoin('subscriptionplans', 'subscriptions.SubscriptionPlanId', '=', 'subscriptionplans.Id')
                          ->orderBy('subscriptionplans.Name', $sortDirection)
                          ->select('stores.*');
                    break;
                case 'renewal_date':
                    $query->leftJoin('subscriptions', 'stores.Id', '=', 'subscriptions.StoreId')
                          ->orderBy('subscriptions.EndTime', $sortDirection)
                          ->select('stores.*');
                    break;
                default:
                    $query->orderBy('stores.CreatedDate', 'desc');
                    break;
            }
        } else {
            $query->orderBy('stores.CreatedDate', 'desc');
        }

        $perPage = $filter['per_page'] ?? config('pagination.per_page');
        $page = $filter['page'] ?? config('pagination.default_page');
        $paginated = $query->paginate($perPage, ['*'], 'page', $page);
        $paginated->appends($filter);

        return $paginated;
    }

    public function getSubscriptionFilterOptions(): array
    {
        $plans = $this->storeRepository->getQuery()
            ->with('subscriptionPlan')
            ->get()
            ->pluck('subscriptionPlan.Name')
            ->filter()
            ->unique()
            ->sort()
            ->values()
            ->toArray();

        array_unshift($plans, 'No Plan');

        $statuses = [
            StoreStatus::ACTIVE->value => 'Actif',
            StoreStatus::INACTIVE->value => 'Inactif'
        ];

        return [
            'plans' => $plans,
            'statuses' => $statuses
        ];
    }

    public function updateSubscription($data, $storeId)
    {
        DB::beginTransaction();
        try {
            $store = Store::findOrFail($storeId);
            $subscriptionData = [
                'SubscriptionPlanId' => $data['plan'],
                'StartTime' => Carbon::parse($data['date_purchase'])->toDateTimeString('microsecond'),
                'EndTime' => Carbon::parse($data['date_renewal'])->toDateTimeString('microsecond'),
                'ModifiedDate' => Carbon::now(),
            ];

            if ($store->subscription) {
                $store->subscription->fill($subscriptionData);
                $store->subscription->save();
            } else {
                Subscription::create(array_merge($subscriptionData, [
                    'Id' => Uuid::uuid4()->toString(),
                    'StoreId' => $store->Id,
                    'State' => 0,
                    'CreatedDate' => Carbon::now(),
                    'IsDeleted' => 0
                ]));
            }

            $now = Carbon::now();
            $startTime = Carbon::parse($subscriptionData['StartTime']);
            $endTime = Carbon::parse($subscriptionData['EndTime']);
            $status = ($now >= $startTime && $now <= $endTime) ? StoreStatus::ACTIVE : StoreStatus::INACTIVE;
            Store::where('Id', $store->Id)->update(['Status' => $status->value]);

            DB::commit();
            return [
                'status' => true,
                'message' => 'Subscription updated successfully!'
            ];
        } catch (\Exception $e) {
            Log::error('Error updating subscription: ' . $e->getMessage());
            DB::rollBack();

            return [
                'status' => false,
                'message' => 'Failed to update subscription.',
                'errors' => [$e->getMessage()]
            ];
        }
    }

    public function saveSchedule(array $data): array
    {
        DB::beginTransaction();
        try {
            Log::info("Received schedule data", ['data' => $data]);

            foreach ($data['stores'] as $key => $item) {
                $store = Store::find($key);
                $schedule = $store->schedule;
                $listDayOfWeek = $schedule->pluck('DayOfWeek')->toArray();

                $allDays = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

                foreach ($allDays as $keySchedule) {
                    $dataSchedule = $item[$keySchedule] ?? null;
                    $dayOfWeek = config('define.days_of_week')[$keySchedule];
                    $existingDay = $schedule->where('DayOfWeek', $dayOfWeek)->first();

                    $dataSession = [
                        'MorningOpenTime' => null,
                        'MorningCloseTime' => null,
                        'AfternoonOpenTime' => null,
                        'AfternoonCloseTime' => null
                    ];

                    if (is_array($dataSchedule) && !empty($dataSchedule)) {
                        foreach ($dataSchedule as $keySession => $valueSession) {
                            $open = Carbon::createFromFormat('Y-m-d H:i', '2024-01-01 ' . $valueSession['open'])->format('Y-m-d H:i:s.u');
                            $close = Carbon::createFromFormat('Y-m-d H:i', '2024-01-01 ' . $valueSession['close'])->format('Y-m-d H:i:s.u');
                            if ($keySession == 0) {
                                $dataSession['MorningOpenTime'] = $open;
                                $dataSession['MorningCloseTime'] = $close;
                            } else {
                                $dataSession['AfternoonOpenTime'] = $open;
                                $dataSession['AfternoonCloseTime'] = $close;
                            }
                        }
                    }

                    $this->handleSaveData($dayOfWeek, $listDayOfWeek, $schedule, $dataSession, $store);
                }
            }
            DB::commit();
            return [
                'status' => true,
                'message' => 'Horaire mis à jour avec succès'
            ];
        } catch (\Exception $e) {
            Log::error('Error saving schedule: ' . $e->getMessage());
            DB::rollBack();

            return [
                'status' => false,
                'message' => 'Failed to saving schedule.',
                'errors' => [$e->getMessage()]
            ];
        }
    }

    public function saveBulkSchedule(array $data): array
    {
        DB::beginTransaction();
        try {
            $template = ScheduleTemplate::find($data['template_id']);
            $dataTemplate = $template->data;
            $countStores = count($data['store_ids']);

            foreach ($data['store_ids'] as $storeId) {
                $store = Store::find($storeId);
                $schedule = $store->schedule;
                $listDayOfWeek = $schedule->pluck('DayOfWeek')->toArray();

                $allDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

                foreach ($allDays as $keyDay) {
                    $valueDay = $dataTemplate[$keyDay] ?? null;
                    $dayOfWeek = config('define.days_of_week')[$keyDay];
                    $existingDay = $schedule->where('DayOfWeek', $dayOfWeek)->first();

                    if (is_array($valueDay)) {
                        $hasMorning = isset($valueDay['morning']) && $valueDay['morning'] && !empty($valueDay['morning']['open']) && !empty($valueDay['morning']['close']);
                        $hasAfternoon = isset($valueDay['afternoon']) && $valueDay['afternoon'] && !empty($valueDay['afternoon']['open']) && !empty($valueDay['afternoon']['close']);

                        if ($hasMorning || $hasAfternoon) {
                            $dataSession = [
                                'MorningOpenTime' => $hasMorning ? Carbon::createFromFormat('Y-m-d H:i', '2024-01-01 ' . $valueDay['morning']['open'])->format('Y-m-d H:i:s.u') : null,
                                'MorningCloseTime' => $hasMorning ? Carbon::createFromFormat('Y-m-d H:i', '2024-01-01 ' . $valueDay['morning']['close'])->format('Y-m-d H:i:s.u') : null,
                                'AfternoonOpenTime' => $hasAfternoon ? Carbon::createFromFormat('Y-m-d H:i', '2024-01-01 ' . $valueDay['afternoon']['open'])->format('Y-m-d H:i:s.u') : null,
                                'AfternoonCloseTime' => $hasAfternoon ? Carbon::createFromFormat('Y-m-d H:i', '2024-01-01 ' . $valueDay['afternoon']['close'])->format('Y-m-d H:i:s.u') : null,
                            ];

                            Log::info("Updating bulk schedule for store {$storeId}, day {$keyDay}", [
                                'dataSession' => $dataSession,
                                'hasMorning' => $hasMorning,
                                'hasAfternoon' => $hasAfternoon
                            ]);

                            $this->handleSaveData($dayOfWeek, $listDayOfWeek, $schedule, $dataSession, $store);
                        } elseif ($existingDay) {
                            $existingDay->delete();
                        }
                    } elseif ($valueDay === null && $existingDay) {
                        $existingDay->delete();
                    }
                }
            }
            DB::commit();
            return [
                'status' => true,
                'message' => $countStores . ($countStores > 1 ? ' stores ' : ' store ') . 'have been successfully updated!'
            ];
        } catch (\Exception $e) {
            Log::error('Error bulk saving schedule: ' . $e->getMessage());
            DB::rollBack();

            return [
                'status' => false,
                'message' => 'Failed to bulk saving schedule.',
                'errors' => [$e->getMessage()]
            ];
        }
    }

    public function getScheduleTemplate()
    {
        return ScheduleTemplate::where('status', 'active')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($template) {
                $dayOrder = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                $template->data = collect($dayOrder)
                    ->mapWithKeys(fn($d) => [$d => $template->data[$d] ?? []])
                    ->toArray();

                return $template;
            });
    }
    public function handleSaveData(mixed $dayOfWeek, $listDayOfWeek, $schedule, array $dataSession, $store): void
    {
        if (in_array($dayOfWeek, $listDayOfWeek)) {
            $day = $schedule->where('DayOfWeek', $dayOfWeek)->first();
            $day->fill($dataSession);
            $day->save();
        } else {
            $dataSession['Id'] = Uuid::uuid4()->toString();
            $dataSession['CreatedDate'] = Carbon::now();
            $dataSession['ModifiedDate'] = Carbon::now();
            $dataSession['IsDeleted'] = 0;
            $dataSession['DayOfWeek'] = $dayOfWeek;
            $dataSession['AddressId'] = $store->address->Id;

            AddressSchedule::create($dataSession);
        }
    }

    public function getStoreWithAllRelations(string $id)
    {
        return $this->storeRepository->getQuery()
            ->with([
                'subscription',
                'subscriptionPlan',
                'address.schedule',
                'employee',
                'business',
                'categories'
            ])
            ->where('Id', $id)
            ->first();
    }

    public function getStoreQuestionsByCategories($id)
    {
        $store = $this->getStoreWithAllRelations($id);
        if (!$store) return [];

        $categoryIds = $store->categories->pluck('Id')->filter()->unique()->values()->toArray();
        if (empty($categoryIds)) return [];

        $questions = [];

        foreach ($categoryIds as $categoryId) {
            $response = $this->vikingStoreSystemService->getStoreQuestionsByCategory($id, $categoryId);

            if (isset($response['body'])) {
                $responseData = json_decode($response['body'], true);
                if (isset($responseData['success']) && $responseData['success'] === true && isset($responseData['data'])) {
                    foreach ($responseData['data'] as $group) {
                        foreach ($group['details'] as $q) {
                            $key = $q['question'];
                            if (!isset($questions[$key])) {
                                $questions[$key] = $q;
                            }
                        }
                    }
                }
            }
        }
        return array_values($questions);
    }

    public function mapStoreQuestionsKey() {
        return [
            'Origin of manufacture of the products?' => 'productManufacturingOrigins',
            'Are you a local Producer?' => 'localProducer',
            'Average budget per person?' => 'averageBudgetPerPerson',
            'Type of cuisine' => 'typeOfCuisines',
            'Age minimum requirements?' => 'minimumAge',
            'Do you sell on street market?' => 'sellOnStreetMarket',
            'Market Informations' => 'markets',
            'Are you proposing Zero Waste offer?' => 'zeroWasteOffer',
            'Choose one or more partner' => 'partners',
        ];
    }

    public function getOrderedPlans()
    {
        $plans = SubscriptionPlan::pluck('name', 'id')->toArray();
        $desiredOrder = config('subscription.plan_order', []);
        $orderedPlans = [];
        foreach ($desiredOrder as $planName) {
            $planId = array_search($planName, $plans);
            if ($planId !== false) {
                $orderedPlans[$planId] = $planName;
            }
        }
        foreach ($plans as $id => $name) {
            if (!in_array($name, $desiredOrder)) {
                $orderedPlans[$id] = $name;
            }
        }
        return $orderedPlans;
    }

    /**
     * Create a new store with transaction support
     *
     * @param array $data
     * @return array
     */
    public function createStore(array $data): array
    {
        DB::beginTransaction();

        try {
            $subcategories = $data['subcategories'] ?? [];

            $planValue = (int) ($data['subscription_plan'] ?? 0);

            $storeData = [
                'name' => $data['name'],
                'companyName' => $data['company_name'],
                'addressLine1' => $data['addressLine1'],
                'addressLine2' => $data['addressLine2'] ?? '',
                'city' => $data['city'],
                'zipCode' => $data['zipCode'],
                'legalOfficer' => $data['representative_name'],
                'sirenNumber' => $data['siren'],
                'apeCode' => '',
                'contactEmail' => $data['email'],
                'phoneNumber' => $data['phone'],
                'latitude' => (float) ($data['latitude'] ?? 0),
                'longitude' => (float) ($data['longitude'] ?? 0),
                'plan' => $planValue,
                'email' => $planValue > SubscriptionLevel::BASIC_TYPE->value ? ($data['store_username'] ?? $data['email']) : $data['email'],
                'password' => $planValue > SubscriptionLevel::BASIC_TYPE->value ? ($data['store_password'] ?? 'Password@123') : '',
            ];

            $response = $this->vikingStoreSystemService->createStore($storeData);

            if (!$response['success'] || !isset($response['data'])) {
                DB::rollBack();
                return $response;
            }

            $storeId = $response['data'];
            if (is_array($storeId)) {
                $storeId = $storeId['result'] ?? $storeId['id'] ?? $storeId['storeId'] ?? null;
                if (!$storeId) {
                    DB::rollBack();
                    return [
                        'success' => false,
                        'message' => 'Invalid store ID format in response',
                        'errors' => ['Could not extract store ID from API response']
                    ];
                }
            }

            if (!empty($subcategories)) {
                $categoryIds = [];

                foreach ($subcategories as $subcategory) {
                    $categoryIds[] = $subcategory;
                }

                if (!empty($categoryIds)) {
                    $categoryData = ['ids' => $categoryIds];
                    $categoryResponse = $this->vikingStoreSystemService->updateCategories($storeId, $categoryData);

                    if (!$categoryResponse['success']) {
                        DB::rollBack();
                        return $categoryResponse;
                    }
                }
            }

            DB::commit();
            return [
                'success' => true,
                'store_id' => $storeId,
                'message' => 'Store created successfully'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating store: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Failed to create store',
                'errors' => [$e->getMessage()]
            ];
        }
    }

    /**
     * Get unique values from a relationship field.
     *
     */
    public function getSubscriptionPlans()
    {
        $plans = SubscriptionPlan::pluck('Name', 'PlanType')->toArray();
        $desiredOrder = config('subscription.plan_order', []);
        $orderedPlans = [];
        foreach ($desiredOrder as $planName) {
            $planId = array_search($planName, $plans);
            if ($planId !== false) {
                $orderedPlans[$planId] = $planName;
            }
        }
        foreach ($plans as $id => $name) {
            if (!in_array($name, $desiredOrder)) {
                $orderedPlans[$id] = $name;
            }
        }
        return $orderedPlans;
    }

    /**
     * Get total count of stores with active subscriptions
     * Uses the same logic as the store list filtering for consistency
     */
    public function getTotalStoresWithSubscription(): int
    {
        $query = $this->storeRepository->getQuery()
            ->where('IsDeleted', StoreStatus::ACTIVE->value);

        $query->whereHas('subscriptionPlan', function($q) {
            $q->whereNotNull('Name')
              ->where('Name', '!=', SubscriptionType::BASIC_PLAN->value);
        });

        return $query->count();
    }

    /**
     * Check if a store exists by email
     *
     * @param string $email
     * @return bool
     */
    public function checkStoreExistsByEmail(string $email): bool
    {
        return $this->storeRepository->getQuery()
            ->whereHas('employee', function($query) use ($email) {
                $query->where('Email', $email);
            })
            ->exists();
    }

    /**
     * Check if a store exists by name, latitude, and longitude
     *
     * @param string $name
     * @param float $latitude
     * @param float $longitude
     * @return bool
     */
    public function checkStoreExistsByLocation(string $name, float $latitude, float $longitude): bool
    {
        return $this->storeRepository->getQuery()
            ->where('Name', $name)
            ->whereHas('address', function($query) use ($latitude, $longitude) {
                $query->where('Latitude', $latitude)
                      ->where('Longitude', $longitude);
            })
            ->exists();
    }
}
