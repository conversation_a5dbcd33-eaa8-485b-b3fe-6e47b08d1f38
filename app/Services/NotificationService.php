<?php

namespace App\Services;

use App\Jobs\IntegrateNotificationJob;
use App\Repositories\Contract\NotificationRepositoryInterface;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Storage;

class NotificationService
{
    protected string $folder = 'notification';
    protected NotificationRepositoryInterface $notificationRepository;

    public function __construct(NotificationRepositoryInterface $notificationRepository)
    {
        $this->notificationRepository = $notificationRepository;
    }

    public function getData(): LengthAwarePaginator
    {
        return $this->notificationRepository->orderBy('CreatedDate', 'DESC')->paginate(10);
    }

    public function store(array $data)
    {
        $image = request()->file('Image');

        if ($image && $image->isValid()) {
            $fileName = uniqueFileName($image->getClientOriginalName(), $this->folder);
            Storage::putFileAs($this->folder, $image, $fileName);
            $data['Image'] = $this->folder . '/' . $fileName;
        }

        IntegrateNotificationJob::dispatch($data);

        return [
            'message' => 'La Notification a bien été envoyée'
        ];
    }
}
