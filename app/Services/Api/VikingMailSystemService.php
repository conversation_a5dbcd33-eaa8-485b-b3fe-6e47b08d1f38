<?php

namespace App\Services\Api;

use App\Services\API;

class VikingMailSystemService
{
    /**
     * @var API
     */
    protected $API;

    protected $domain;

    protected $headers = [];

    public function __construct(API $API)
    {
        $this->API = $API;
        $this->domain = config('vikingapp.api.domain');
        $this->headers = [
            'x-api-key' => config('vikingapp.api.secret_key'),
            'Content-Type' => 'application/json'
        ];
    }

    /**
     * @return mixed
     */
    public function sendMail($body)
    {
        $endpoint = config('vikingapp.api.send-mail');
        $api = $this->domain . $endpoint;

        return $this->API->sendAsync($api, 'POST', $this->headers, json_encode($body));
    }
}
