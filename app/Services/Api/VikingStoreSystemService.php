<?php

namespace App\Services\Api;

use App\Services\API;
use App\Services\ApiUrlService;
use App\Models\Store;
use GuzzleHttp\Psr7\MultipartStream;
use Illuminate\Support\Facades\Log;

class VikingStoreSystemService
{
    /**
     * @var API
     */
    protected $API;

    protected $domain;

    protected $headers = [];

    public function __construct(API $API)
    {
        $this->API = $API;
        $this->domain = config('vikingapp.api.domain');
        $this->headers = [
            'x-api-key' => config('vikingapp.api.secret_key')
        ];
    }

    /**
     * @return mixed
     */
    public function import($body)
    {
        $endpoint = config('vikingapp.api.import');
        $api = $this->domain . $endpoint;

        return $this->API->sendAsync($api, 'POST', $this->headers, new MultipartStream($body));
    }

    public function validate($body)
    {
        $endpoint = config('vikingapp.api.validate');
        $api = $this->domain . $endpoint;

        return $this->API->sendAsync($api, 'POST', $this->headers, new MultipartStream($body));
    }

    public function getStoreQuestionsByCategory($id, $categoryId)
    {
        $api = $this->buildApiUrl('store-category-questions', $id);

        $headers = $this->headers;
        $headers['Content-Type'] = 'application/json';

        $requestData = [
            'categoryId' => [$categoryId]
        ];
        $jsonBody = json_encode($requestData);
        return $this->API->sendAsync($api, 'POST', $headers, $jsonBody);
    }

    private function getApiData($endpoint, $id = null)
    {
        $api = $this->buildApiUrl($endpoint, $id);

        $headers = $this->headers;
        $headers['Content-Type'] = 'application/json';

        $response = $this->API->sendAsync($api, 'GET', $headers);
        if (isset($response['body'])) {
            $responseData = json_decode($response['body'], true);
            if (isset($responseData['success']) &&
                $responseData['success'] === true &&
                isset($responseData['data'])) {
                return $responseData['data'];
            }
        }
        return $response;
    }

    public function getStoreMasterData()
    {
        return $this->getApiData('store-master-data');
    }

    public function getStoreDetail($id)
    {
        $api = $this->buildApiUrl('store-detail', $id);

        $headers = $this->headers;
        $headers['Content-Type'] = 'application/json';

        $response = $this->API->sendAsync($api, 'GET', $headers);
        if (isset($response['body'])) {
            $jsonString = $response['body'];
            $jsonString = preg_replace_callback(
                '/"(latitude|longitude)"\s*:\s*([0-9.-]+)/',
                function($matches) {
                    return '"' . $matches[1] . '":"' . $matches[2] . '"';
                },
                $jsonString
            );

            $responseData = json_decode($jsonString, true);
            if (isset($responseData['success']) &&
                $responseData['success'] === true &&
                isset($responseData['data'])) {
                return $responseData['data'];
            }
        }
        return $response;
    }

    public function getStoreServices($id)
    {
        return $this->getApiData('store-service', $id);
    }

    public function getStoreCategories($id)
    {
        return $this->getApiData('store-categories', $id);
    }

    public function getAllStores($page = 1, $perPage = 10)
    {
        try {
            $storesFromDb = Store::paginate($perPage, ['*'], 'page', $page);
            $allStoresData = [];

            foreach ($storesFromDb->items() as $storeRecord) {
                $storeId = $storeRecord->Id;
                $storeDetailFromApi = $this->getStoreDetail($storeId);

                if ($storeDetailFromApi &&
                    isset($storeDetailFromApi) &&
                    !isset($storeDetailFromApi['error'])) {
                    $allStoresData[] = $storeDetailFromApi;
                }
            }

            return [
                'data' => $allStoresData,
                'pagination' => [
                    'total' => $storesFromDb->total(),
                    'per_page' => $storesFromDb->perPage(),
                    'current_page' => $storesFromDb->currentPage(),
                    'last_page' => $storesFromDb->lastPage(),
                    'from' => $storesFromDb->firstItem(),
                    'to' => $storesFromDb->lastItem(),
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get all stores: ' . $e->getMessage());
            return [
                'data' => [],
                'pagination' => [
                    'total' => 0,
                    'per_page' => $perPage,
                    'current_page' => $page,
                    'last_page' => 1,
                    'from' => 0,
                    'to' => 0,
                ]
            ];
        }
    }

    /**
     * Get all stores from database with search and filter capabilities
     *
     * @param string $search
     * @param array $cities
     * @param int $page
     * @param int $perPage
     * @return array
     */
    public function getAllStoresWithFilters(string $search = '', array $cities = [], int $page = 1, int $perPage = 25): array
    {
        try {
            // Build query with filters
            $query = Store::query()
                ->with(['address', 'categories', 'address.schedule']);

            // Apply search filter
            if (!empty($search)) {
                $query->where('Name', 'like', '%' . $search . '%');
            }

            // Apply cities filter (multi-select)
            if (!empty($cities)) {
                $query->whereHas('address', function($q) use ($cities) {
                    $q->whereIn('City', $cities);
                });
            }

            // Get paginated results
            $storesFromDb = $query->paginate($perPage, ['*'], 'page', $page);
            $allStoresData = [];

            // Get API details for each store
            foreach ($storesFromDb->items() as $storeRecord) {
                $storeId = $storeRecord->Id;
                $storeDetailFromApi = $this->getStoreDetail($storeId);

                if ($storeDetailFromApi &&
                    !isset($storeDetailFromApi['error'])) {
                    $allStoresData[] = $storeDetailFromApi;
                }
            }

            return [
                'data' => $allStoresData,
                'pagination' => [
                    'total' => $storesFromDb->total(),
                    'per_page' => $storesFromDb->perPage(),
                    'current_page' => $storesFromDb->currentPage(),
                    'last_page' => $storesFromDb->lastPage(),
                    'from' => $storesFromDb->firstItem(),
                    'to' => $storesFromDb->lastItem(),
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get stores with filters: ' . $e->getMessage());
            return [
                'data' => [],
                'pagination' => [
                    'total' => 0,
                    'per_page' => $perPage,
                    'current_page' => $page,
                    'last_page' => 1,
                    'from' => 0,
                    'to' => 0,
                ]
            ];
        }
    }

    /**
     * Get available cities for filter dropdown
     *
     * @return array
     */
    public function getAvailableCities(): array
    {
        try {
            return Store::query()
                ->join('addresses', 'stores.Id', '=', 'addresses.StoreId')
                ->whereNotNull('addresses.City')
                ->where('addresses.City', '!=', '')
                ->distinct()
                ->pluck('addresses.City')
                ->map(function($city) {
                    return strtoupper($city);
                })
                ->unique()
                ->sort()
                ->values()
                ->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to get available cities: ' . $e->getMessage());
            return [];
        }
    }

    private function buildApiUrl($endpoint, $id = null): string
    {
        return ApiUrlService::build($endpoint, $id);
    }

    /**
     * Update store basic information
     *
     * @param string $storeId
     * @param array $data
     * @return array
     */
    public function updateBasicInfo(string $storeId, array $data): array
    {
        return $this->performJsonUpdate('store-update', $storeId, $data, 'Store updated successfully', 'Error updating store basic info');
    }

    /**
     * Update store categories
     *
     * @param string $storeId
     * @param array $data
     * @return array
     */
    public function updateCategories(string $storeId, array $data): array
    {
        return $this->performJsonUpdate('store-update-categories', $storeId, $data, 'Categories updated successfully', 'Error updating store categories');
    }

    /**
     * Update store social networks
     *
     * @param string $storeId
     * @param array $data
     * @return array
     */
    public function updateSocial(string $storeId, array $data): array
    {
        return $this->performJsonUpdate('store-update-social', $storeId, $data, 'Social networks updated successfully', 'Error updating store social networks');
    }

    /**
     * Update store schedules
     *
     * @param string $storeId
     * @param array $data
     * @return array
     */
    public function updateSchedules(string $storeId, array $data): array
    {
        array_walk_recursive($data, function (&$value) {
            if (is_null($value)) {
                $value = '';
            }
        });
        return $this->performJsonUpdate('store-update-schedules', $storeId, $data, 'Schedules updated successfully', 'Error updating store schedules');
    }

    /**
     * Update store description
     *
     * @param string $storeId
     * @param array $data
     * @return array
     */
    public function updateDescription(string $storeId, array $data): array
    {
        return $this->performJsonUpdate('store-update-description', $storeId, $data, 'Description updated successfully', 'Error updating store description');
    }

    /**
     * Update store services
     *
     * @param string $storeId
     * @param array $data
     * @return array
     */
    public function updateServices(string $storeId, array $data): array
    {
        return $this->performJsonUpdate('store-update-services', $storeId, $data, 'Services updated successfully', 'Error updating store services');
    }

    /**
     * Update store additional information/answers
     *
     * @param string $storeId
     * @param array $data
     * @return array
     */
    public function updateAnswers(string $storeId, array $data): array
    {
        return $this->performJsonUpdate('store-update-answers', $storeId, $data, 'Additional information updated successfully', 'Error updating store answers');
    }

    /**
     * Update store subscription plan
     *
     * @param string $storeId
     * @param array $data
     * @return array
     */
    public function updateSubscription(string $storeId, array $data): array
    {
        return $this->performJsonUpdate('store-update-subscription', $storeId, $data, 'Subscription plan updated successfully', 'Error updating store subscription');
    }

    /**
     * Delete a store
     *
     * @param string $storeId
     * @return array
     */
    public function deleteStore(string $storeId): array
    {
        try {
            $api = $this->buildApiUrl('store-delete', $storeId);

            $headers = $this->headers;
            $headers['Content-Type'] = 'application/json';

            $response = $this->API->sendAsync($api, 'DELETE', $headers);

            return $this->handleApiResponse($response, 'Store deleted successfully');

        } catch (\Exception $e) {
            Log::error('Error deleting store: ' . $e->getMessage());
            return $this->handleApiError($e, 'An error occurred while deleting store');
        }
    }

    /**
     * Perform a JSON-based update request
     *
     * @param string $endpoint
     * @param string $storeId
     * @param array $data
     * @param string $successMessage
     * @param string $errorLogPrefix
     * @param string $method
     * @return array
     */
    private function performJsonUpdate(string $endpoint, string $storeId, array $data, string $successMessage, string $errorLogPrefix, string $method = 'PUT'): array
    {
        try {
            $api = $this->buildApiUrl($endpoint, $storeId);

            $headers = $this->headers;
            $headers['Content-Type'] = 'application/json';

            $jsonBody = json_encode($data);

            $response = $this->API->sendAsync($api, $method, $headers, $jsonBody);

            return $this->handleApiResponse($response, $successMessage);

        } catch (\Exception $e) {
            Log::error($errorLogPrefix . ': ' . $e->getMessage());
            return $this->handleApiError($e, 'An error occurred while updating');
        }
    }

    /**
     * Add a new photo to store
     *
     * @param string $storeId
     * @param \Illuminate\Http\UploadedFile $image
     * @param \Illuminate\Http\UploadedFile $resizeImage
     * @param bool $isPrimary
     * @return array
     */
    public function addPhoto(string $storeId, $image, $resizeImage, bool $isPrimary): array
    {
        try {
            $api = $this->buildApiUrl('store-add-image');

            $headers = $this->headers;
            unset($headers['Content-Type']);

            $multipartData = [
                [
                    'name' => 'StoreId',
                    'contents' => $storeId
                ],
                [
                    'name' => 'IsPrimary',
                    'contents' => $isPrimary ? 'true' : 'false'
                ]
            ];

            if ($image && $image->isValid()) {
                $multipartData[] = [
                    'name' => 'Image',
                    'contents' => fopen($image->getPathname(), 'r'),
                    'filename' => $image->getClientOriginalName()
                ];
            }

            if ($resizeImage && $resizeImage->isValid()) {
                $multipartData[] = [
                    'name' => 'ResizeImage',
                    'contents' => fopen($resizeImage->getPathname(), 'r'),
                    'filename' => $resizeImage->getClientOriginalName()
                ];
            }

            $response = $this->API->sendAsync($api, 'POST', $headers, new MultipartStream($multipartData));

            return $this->handleApiResponse($response, 'Photo ajoutée avec succès');

        } catch (\Exception $e) {
            Log::error('Error adding store photo: ' . $e->getMessage());
            return $this->handleApiError($e, 'Une erreur s\'est produite lors de l\'ajout de la photo');
        }
    }

    /**
     * Delete a photo from store
     *
     * @param string $storeId
     * @param string $imageId
     * @return array
     */
    public function deletePhoto(string $storeId, string $imageId): array
    {
        try {
            $api = $this->buildApiUrl('store-delete-image');

            $headers = $this->headers;
            $headers['Content-Type'] = 'application/json';

            $data = [
                'storeId' => $storeId,
                'imageId' => $imageId
            ];

            $response = $this->API->sendAsync($api, 'DELETE', $headers, json_encode($data));

            return $this->handleApiResponse($response, 'Photo supprimée avec succès');

        } catch (\Exception $e) {
            Log::error('Error deleting store photo: ' . $e->getMessage());
            return $this->handleApiError($e, 'Une erreur s\'est produite lors de la suppression de la photo');
        }
    }

    /**
     * Replace an existing photo in store
     *
     * @param string $storeId
     * @param string $imageId
     * @param \Illuminate\Http\UploadedFile $image
     * @param \Illuminate\Http\UploadedFile $resizeImage
     * @param bool $isPrimary
     * @return array
     */
    public function replacePhoto(string $storeId, string $imageId, $image, $resizeImage, bool $isPrimary): array
    {
        try {
            $api = $this->buildApiUrl('store-update-image');

            $headers = $this->headers;
            unset($headers['Content-Type']);

            $multipartData = [
                [
                    'name' => 'StoreId',
                    'contents' => $storeId
                ],
                [
                    'name' => 'ImageId',
                    'contents' => $imageId
                ],
                [
                    'name' => 'IsPrimary',
                    'contents' => $isPrimary ? 'true' : 'false'
                ]
            ];

            if ($image && $image->isValid()) {
                $multipartData[] = [
                    'name' => 'Image',
                    'contents' => fopen($image->getPathname(), 'r'),
                    'filename' => $image->getClientOriginalName()
                ];
            }

            if ($resizeImage && $resizeImage->isValid()) {
                $multipartData[] = [
                    'name' => 'ResizeImage',
                    'contents' => fopen($resizeImage->getPathname(), 'r'),
                    'filename' => $resizeImage->getClientOriginalName()
                ];
            }

            $response = $this->API->sendAsync($api, 'POST', $headers, new MultipartStream($multipartData));

            return $this->handleApiResponse($response, 'Photo remplacée avec succès');

        } catch (\Exception $e) {
            Log::error('Error replacing store photo: ' . $e->getMessage());
            return $this->handleApiError($e, 'Une erreur s\'est produite lors du remplacement de la photo');
        }
    }

    /**
     * Update store photos (legacy method - kept for backward compatibility)
     *
     * @param string $storeId
     * @param array $files
     * @param string|null $primaryImage
     * @return array
     */
    public function updatePhotos(string $storeId, array $files = [], string $primaryImage = null): array
    {
        try {
            $api = $this->buildApiUrl('store-add-image');

            $headers = $this->headers;

            unset($headers['Content-Type']);

            $multipartData = $this->buildMultipartData($storeId, $files, $primaryImage);

            $response = $this->API->sendAsync($api, 'POST', $headers, new MultipartStream($multipartData));

            return $this->handleApiResponse($response, 'Photos updated successfully');

        } catch (\Exception $e) {
            Log::error('Error updating store photos: ' . $e->getMessage());
            return $this->handleApiError($e, 'An error occurred while updating photos');
        }
    }

    /**
     * Build multipart data for photo upload
     *
     * @param string $storeId
     * @param array $files
     * @param string|null $primaryImage
     * @return array
     */
    private function buildMultipartData(string $storeId, array $files, string $primaryImage = null): array
    {
        $multipartData = [];

        $multipartData[] = [
            'name' => 'storeId',
            'contents' => $storeId
        ];

        foreach ($files as $index => $file) {
            if ($file && $file->isValid()) {
                $multipartData[] = [
                    'name' => "photos[$index]",
                    'contents' => fopen($file->getPathname(), 'r'),
                    'filename' => $file->getClientOriginalName()
                ];
            }
        }

        if ($primaryImage) {
            $multipartData[] = [
                'name' => 'primaryImage',
                'contents' => $primaryImage
            ];
        }

        return $multipartData;
    }

    /**
     * Handle API response
     *
     * @param array $response
     * @param string $successMessage
     * @param string $errorMessage
     * @return array
     */
    private function handleApiResponse(array $response, string $successMessage, string $errorMessage = 'Failed to update'): array
    {
        if (isset($response['body'])) {
            $responseData = json_decode($response['body'], true);

            if (isset($responseData['success']) && $responseData['success'] === true) {
                $data = $responseData['data'] ?? [];
                if (!is_array($data)) {
                    $data = ['result' => $data];
                }

                return $this->buildApiResponse(
                    true,
                    $successMessage,
                    $data
                );
            } else {
                return $this->buildApiResponse(
                    false,
                    $responseData['message'] ?? $errorMessage,
                    [],
                    $responseData['errors'] ?? []
                );
            }
        }

        return $this->buildApiResponse(false, 'Invalid response from API', [], []);
    }

    /**
     * Create a new store
     *
     * @param array $data
     * @return array
     */
    public function createStore(array $data): array
    {
        try {
            $api = $this->buildApiUrl('store-create');

            $headers = $this->headers;
            $headers['Content-Type'] = 'application/json';

            $jsonBody = json_encode($data);

            $response = $this->API->sendAsync($api, 'POST', $headers, $jsonBody);

            return $this->handleApiResponse($response, 'Store created successfully');

        } catch (\Exception $e) {
            Log::error('Store creation API error: ' . $e->getMessage());
            return $this->handleApiError($e, 'Failed to create store');
        }
    }

    /**
     * Handle API error
     *
     * @param \Exception $e
     * @param string $message
     * @return array
     */
    private function handleApiError(\Exception $e, string $message): array
    {
        return [
            'success' => false,
            'message' => $message,
            'errors' => [$e->getMessage()]
        ];
    }

    /**
     * Download file from API
     *
     * @param string $fileId
     * @return array
     */
    public function downloadFile(string $fileId): array
    {
        try {
            $api = $this->buildApiUrl('file-download', $fileId);

            $headers = $this->headers;
            $headers['Content-Type'] = 'application/json';

            $response = $this->API->sendAsync($api, 'GET', $headers);

            return $this->handleFileDownloadResponse($response);

        } catch (\Exception $e) {
            return $this->handleApiError($e, 'Failed to download file from API');
        }
    }

    /**
     * Handle file download response
     *
     * @param array $response
     * @return array
     */
    private function handleFileDownloadResponse(array $response): array
    {
        if (!isset($response['body'])) {
            return [
                'success' => false,
                'message' => 'Invalid response from file download API',
                'errors' => []
            ];
        }

        $responseData = json_decode($response['body'], true);
        $jsonError = json_last_error();

        if ($jsonError !== JSON_ERROR_NONE) {
            $fileType = detectFileType($response['body']);
            $fileSize = formatFileSize(strlen($response['body']));

            return [
                'success' => true,
                'message' => 'File downloaded successfully (binary content)',
                'data' => [
                    'file_name' => 'downloaded_file',
                    'file_type' => $fileType,
                    'file_size' => $fileSize
                ],
                'file_content' => $response['body'],
                'file_name' => 'downloaded_file',
                'file_type' => $fileType,
                'file_size' => $fileSize,
                'is_binary' => true
            ];
        }

        if (isset($responseData['success']) && $responseData['success'] === true) {
            return [
                'success' => true,
                'message' => 'File downloaded successfully',
                'data' => $responseData['data'] ?? null,
                'file_content' => $responseData['file_content'] ?? null,
                'file_name' => $responseData['file_name'] ?? null,
                'file_type' => $responseData['file_type'] ?? null,
                'file_size' => $responseData['file_size'] ?? null
            ];
        }

        return [
            'success' => false,
            'message' => $responseData['message'] ?? 'Failed to download file',
            'errors' => $responseData['errors'] ?? []
        ];
    }

    /**
     * Process store business claim status update
     *
     * @param string $claimId
     * @param int $claimBusinessStatus
     * @return bool
     */
    public function processStoreClaim(string $claimId, int $claimBusinessStatus): bool
    {
        try {
            $endpoint = config('vikingapp.api.store-claim-process');
            $api = $this->domain . $endpoint;

            $headers = $this->headers;
            $headers['Content-Type'] = 'application/json';

            $requestData = [
                'id' => $claimId,
                'claimBusinessStatus' => $claimBusinessStatus
            ];

            $jsonBody = json_encode($requestData);
            $response = $this->API->sendAsync($api, 'POST', $headers, $jsonBody);

            if (isset($response['body']) &&
                ($responseData = json_decode($response['body'], true)) &&
                isset($responseData['success']) &&
                $responseData['success'] === true) {
                return true;
            }
            return false;

        } catch (\Exception $e) {
            Log::error('Failed to process store claim : ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get list of holiday events for a store
     *
     * @param string $storeId
     * @param array $filters
     * @return array
     */
    public function getHolidayEvents(string $storeId, array $filters = []): array
    {
        try {
            $endpoint = config('vikingapp.api.holiday-event-get-list');
            $api = $this->domain . $endpoint;

            $headers = $this->headers;
            $headers['Content-Type'] = 'application/json';

            $isAscending = false;
            if (isset($filters['isAscending'])) {
                if (is_string($filters['isAscending'])) {
                    $isAscending = strtolower($filters['isAscending']) === 'true' || $filters['isAscending'] === '1';
                } else {
                    $isAscending = (bool)$filters['isAscending'];
                }
            }

            $requestData = [
                'storeId' => $storeId,
                'page' => (int)($filters['page'] ?? 0),
                'pageSize' => (int)($filters['pageSize'] ?? 0),
                'sortBy' => $filters['sortBy'] ?? '',
                'isAscending' => $isAscending,
                'search' => $filters['search'] ?? ''
            ];

            $jsonBody = json_encode($requestData);
            $response = $this->API->sendAsync($api, 'POST', $headers, $jsonBody);

            if (isset($response['body'])) {
                $responseData = json_decode($response['body'], true);
                if (isset($responseData['success']) && $responseData['success'] === true) {
                    return $responseData['data'] ?? [];
                }
            }

            return ['items' => [], 'metadata' => ['totalItem' => 0, 'pageSize' => 10, 'page' => 1]];
        } catch (\Exception $e) {
            Log::error('Failed to get holiday events: ' . $e->getMessage());
            return ['items' => [], 'metadata' => ['totalItem' => 0, 'pageSize' => 10, 'page' => 1]];
        }
    }

    /**
     * Get holiday event detail
     *
     * @param string $eventId
     * @return array
     */
    public function getHolidayEventDetail(string $eventId): array
    {
        try {
            $api = $this->buildApiUrl('holiday-event-detail', $eventId);

            $headers = $this->headers;
            $headers['Content-Type'] = 'application/json';

            $response = $this->API->sendAsync($api, 'GET', $headers);

            if (isset($response['body'])) {
                $responseData = json_decode($response['body'], true);
                if (isset($responseData['success']) && $responseData['success'] === true) {
                    return $responseData['data'] ?? [];
                }
            }

            return [];
        } catch (\Exception $e) {
            Log::error('Failed to get holiday event detail: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Create holiday event
     *
     * @param array $data
     * @return array
     */
    public function createHolidayEvent(array $data): array
    {
        return $this->performHolidayEventOperation(
            'holiday-event-create',
            'POST',
            $data,
            'Événement créé avec succès',
            'Échec de la création de l\'événement',
            'Failed to create holiday event'
        );
    }

    /**
     * Update holiday event
     *
     * @param string $eventId
     * @param array $data
     * @return array
     */
    public function updateHolidayEvent(string $eventId, array $data): array
    {
        return $this->performHolidayEventOperation(
            'holiday-event',
            'PUT',
            $data,
            'Événement mis à jour avec succès',
            'Échec de la mise à jour de l\'événement',
            'Failed to update holiday event',
            $eventId
        );
    }

    /**
     * Update holiday event status
     *
     * @param string $eventId
     * @param int $status
     * @return array
     */
    public function updateHolidayEventStatus(string $eventId, int $status): array
    {
        try {
            $api = $this->buildApiUrl('holiday-event-status-update', $eventId);

            $headers = $this->headers;
            $headers['Content-Type'] = 'application/json';

            $requestData = ['status' => $status];
            $jsonBody = json_encode($requestData);
            $response = $this->API->sendAsync($api, 'PUT', $headers, $jsonBody);

            return $this->handleApiResponse(
                $response,
                'Statut de l\'événement mis à jour avec succès',
                'Échec de la mise à jour du statut'
            );

        } catch (\Exception $e) {
            Log::error('Failed to update holiday event status: ' . $e->getMessage());
            return $this->handleApiError($e, 'Une erreur s\'est produite lors de la mise à jour du statut');
        }
    }

    /**
     * Delete holiday event
     *
     * @param string $eventId
     * @return array
     */
    public function deleteHolidayEvent(string $eventId): array
    {
        try {
            $api = $this->buildApiUrl('holiday-event', $eventId);

            $headers = $this->headers;
            $headers['Content-Type'] = 'application/json';

            $response = $this->API->sendAsync($api, 'DELETE', $headers);

            return $this->handleApiResponse(
                $response,
                'Événement supprimé avec succès',
                'Échec de la suppression de l\'événement'
            );

        } catch (\Exception $e) {
            Log::error('Failed to delete holiday event: ' . $e->getMessage());
            return $this->handleApiError($e, 'Une erreur s\'est produite lors de la suppression de l\'événement');
        }
    }

    /**
     * Common holiday event operation handler
     *
     * @param string $endpoint
     * @param string $method
     * @param array $data
     * @param string $successMessage
     * @param string $errorMessage
     * @param string $logPrefix
     * @param string|null $eventId
     * @return array
     */
    private function performHolidayEventOperation(
        string $endpoint,
        string $method,
        array $data,
        string $successMessage,
        string $errorMessage,
        string $logPrefix,
        string $eventId = null
    ): array {
        try {
            $requiredFields = ['storeId', 'eventName', 'startDate'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    return $this->buildApiResponse(
                        false,
                        "Le champ '$field' est requis",
                        [],
                        ["Le champ '$field' est requis"]
                    );
                }
            }

            $api = $eventId ? $this->buildApiUrl($endpoint, $eventId) : $this->domain . config("vikingapp.api.$endpoint");

            $headers = $this->headers;
            $headers['Content-Type'] = 'application/json';

            $requestBody = $this->buildHolidayEventRequestBody($data);

            $jsonBody = json_encode($requestBody);
            $response = $this->API->sendAsync($api, $method, $headers, $jsonBody);

            return $this->handleApiResponse($response, $successMessage, $errorMessage);

        } catch (\Exception $e) {
            Log::error("$logPrefix: " . $e->getMessage());
            return $this->handleApiError($e, 'Une erreur s\'est produite lors de l\'opération sur l\'événement');
        }
    }

    /**
     * Build request body for holiday event operations
     *
     * @param array $data
     * @return array
     */
    private function buildHolidayEventRequestBody(array $data): array
    {
        $startDate = $data['startDate'];
        $endDate = $data['endDate'] ?? null;
        $requestBody = [
            'storeId' => $data['storeId'],
            'eventName' => $data['eventName'],
            'startDate' => $startDate,
            'endDate' => $endDate,
            'isClosedAllDay' => isset($data['isClosedAllDay']) ? filter_var($data['isClosedAllDay'], FILTER_VALIDATE_BOOLEAN) : false,
            'morningOpenTime' => $data['morningOpenTime'] ?? null,
            'morningCloseTime' => $data['morningCloseTime'] ?? null,
            'afternoonOpenTime' => $data['afternoonOpenTime'] ?? null,
            'afternoonCloseTime' => $data['afternoonCloseTime'] ?? null
        ];

        if ($requestBody['isClosedAllDay']) {
            $requestBody['morningOpenTime'] = null;
            $requestBody['morningCloseTime'] = null;
            $requestBody['afternoonOpenTime'] = null;
            $requestBody['afternoonCloseTime'] = null;
        }

        return $requestBody;
    }



    /**
     * Build standardized API response
     *
     * @param bool $success
     * @param string $message
     * @param array $data
     * @param array $errors
     * @return array
     */
    private function buildApiResponse(bool $success, string $message, array $data = [], array $errors = []): array
    {
        return [
            'success' => $success,
            'message' => $message,
            'data' => $data,
            'errors' => $errors
        ];
    }
}
