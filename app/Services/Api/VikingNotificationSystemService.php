<?php

namespace App\Services\Api;

use App\Services\API;
use GuzzleHttp\Psr7\MultipartStream;

class VikingNotificationSystemService
{
    /**
     * @var API
     */
    protected $API;

    protected $domain;

    protected $headers = [];

    public function __construct(API $API)
    {
        $this->API = $API;
        $this->domain = config('vikingapp.api.domain');
        $this->headers = [
            'x-api-key' => config('vikingapp.api.secret_key')
        ];
    }

    /**
     * @return mixed
     */
    public function triggerNotify($body)
    {
        $endpoint = config('vikingapp.api.trigger-schedule');
        $api = $this->domain . $endpoint;

        return $this->API->sendAsync($api, 'POST', $this->headers, new MultipartStream($body));
    }
}
