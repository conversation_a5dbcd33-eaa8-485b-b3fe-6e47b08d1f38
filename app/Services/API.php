<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class API
{
    protected Client $client;

    public function __construct()
    {
        $this->client = new Client();
    }

    /**
     * @param $url
     * @param string $method
     * @param array $headers
     * @param null $body
     * @return mixed
     */
    public function sendAsync($url, string $method = 'GET', array $headers = [], $body = null)
    {
        $request = new Request($method, $url, $headers, $body);
        $promise = $this->client->sendAsync($request)->then(function (ResponseInterface $response) {
            return [
                'status' => $response->getStatusCode(),
                'body' => $response->getBody()->getContents(),
            ];
        }, function ($e) use ($url) {
            if ($e instanceof ServerException || $e instanceof ClientException) {
                if ($e->hasResponse()) {
                    $response = $e->getResponse();
                    Log::error(
                        "API [$url] ERROR: ",
                        [$response->getBody()->getContents()]
                    );
                    return [
                        'status' => $response->getStatusCode(),
                        'messages' => $response->getBody()->getContents(),
                    ];
                }
            }

            Log::error(
                "API [$url] ERROR: ",
                [$e->getMessage()]
            );
            return [
                'status' => ResponseAlias::HTTP_INTERNAL_SERVER_ERROR,
                'messages' => $e->getMessage(),
            ];
        });

        return $promise->wait();
    }
}
