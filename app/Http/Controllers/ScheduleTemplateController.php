<?php

namespace App\Http\Controllers;

use App\Exceptions\ScheduleTemplate\MaximumTemplatesExceededException;
use App\Exceptions\ScheduleTemplate\ScheduleTemplateException;
use App\Exceptions\ScheduleTemplate\TemplateNotFoundException;
use App\Http\Requests\ScheduleTemplateRequest;
use App\Services\ScheduleTemplateService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ScheduleTemplateController extends Controller
{
    public function __construct(protected ScheduleTemplateService $scheduleTemplateService)
    {
    }

    public function index(): JsonResponse
    {
        try {
            $templates = $this->scheduleTemplateService->getAllTemplates();

            return response()->json([
                'success' => true,
                'data' => $templates
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get schedule templates: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to load schedule templates: ' . $e->getMessage(),
                'data' => []
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function store(ScheduleTemplateRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $template = $this->scheduleTemplateService->createTemplate($data);

            return response()->json([
                'success' => true,
                'message' => 'Template créé avec succès',
                'data' => $template,
                'refresh_templates' => true
            ]);
        } catch (MaximumTemplatesExceededException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function show(int $id): JsonResponse
    {
        try {
            $template = $this->scheduleTemplateService->getTemplate($id);

            return response()->json([
                'success' => true,
                'data' => $template
            ]);
        } catch (TemplateNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function update(ScheduleTemplateRequest $request, int $id): JsonResponse
    {
        try {
            $data = $request->validated();
            $template = $this->scheduleTemplateService->updateTemplate($id, $data);

            return response()->json([
                'success' => true,
                'message' => 'Template mis à jour avec succès',
                'data' => $template,
                'refresh_templates' => true
            ]);
        } catch (TemplateNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function destroy(int $id): JsonResponse
    {
        try {
            $this->scheduleTemplateService->deleteTemplate($id);

            return response()->json([
                'success' => true,
                'message' => 'Template supprimé avec succès',
                'refresh_templates' => true
            ]);
        } catch (TemplateNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function deactivate(int $id): JsonResponse
    {
        try {
            $template = $this->scheduleTemplateService->deactivateTemplate($id);

            return response()->json([
                'success' => true,
                'message' => 'Template désactivé avec succès',
                'data' => $template,
                'refresh_templates' => true
            ]);
        } catch (TemplateNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function reactivate(int $id): JsonResponse
    {
        try {
            $template = $this->scheduleTemplateService->reactivateTemplate($id);

            return response()->json([
                'success' => true,
                'message' => 'Template réactivé avec succès',
                'data' => $template,
                'refresh_templates' => true
            ]);
        } catch (TemplateNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
