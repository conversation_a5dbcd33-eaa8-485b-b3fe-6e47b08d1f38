<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSubscriptionRequest;
use App\Services\StoreService;
use Illuminate\Http\Request;

class SubscriptionController extends Controller
{
    protected StoreService $storeService;

    public function __construct(StoreService $storeService)
    {
        $this->storeService = $storeService;
    }

    public function index()
    {
        $plans = $this->storeService->getOrderedPlans();
        return view('subscription.index', [
            'plans' => $plans
        ]);
    }

    public function filterList(Request $request)
    {
        $params = $request->all();
        $stores = $this->storeService->listSubscription($params);

        return view('subscription.partials.list', [
            'stores' => $stores
        ])->render();
    }

    public function filterOverview(Request $request)
    {
        $params = $request->all();
        $params['per_page'] = config('pagination.per_page');
        $stores = $this->storeService->listSubscriptionWithFilters($params);
        $filterOptions = $this->storeService->getSubscriptionFilterOptions();

        return view('subscription.partials.overview', [
            'stores' => $stores,
            'filterOptions' => $filterOptions,
            'currentFilters' => $params
        ])->render();
    }

    public function filterReminder(Request $request)
    {
        $params = $request->all();
        $params['per_page'] = config('pagination.per_page');
        $stores = $this->storeService->listSubscription($params);

        return view('subscription.partials.reminder', [
            'stores' => $stores
        ])->render();
    }

    public function update(StoreSubscriptionRequest $request, string $storeId)
    {
        $data = $request->validated();

        $response = $this->storeService->updateSubscription($data, $storeId);

        if ($response['status']) {
            return response()->json([
                'status' => true,
                'message' => $response['message'],
                'redirect_url' => route('subscription.index')
            ]);
        }

        return response()->json([
            'status' => false,
            'message' => $response['message'],
            'errors' => $response['errors'],
        ], 400);
    }
}
