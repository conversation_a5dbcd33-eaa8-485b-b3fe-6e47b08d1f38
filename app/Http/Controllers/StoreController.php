<?php

namespace App\Http\Controllers;

use App\Enums\StoreBusinessClaimStatus;
use App\Enums\StoreStatus;
use App\Enums\SubscriptionLevel;
use App\Http\Requests\GetStoresDataRequest;
use App\Http\Requests\ImportStoreRequest;
use App\Http\Requests\StoreCreateRequest;
use App\Http\Requests\StoreIndexRequest;
use App\Http\Requests\StoreScheduleBulkRequest;
use App\Http\Requests\StoreScheduleRequest;
use App\Http\Requests\StoreUpdateRequest;
use App\Http\Requests\StoreCategoriesUpdateRequest;
use App\Http\Requests\StorePhotosUpdateRequest;
use App\Http\Requests\StoreSocialUpdateRequest;
use App\Http\Requests\StoreSchedulesUpdateRequest;
use App\Http\Requests\StoreDescriptionUpdateRequest;
use App\Http\Requests\StoreServicesUpdateRequest;
use App\Http\Requests\StoreAnswersUpdateRequest;
use App\Http\Requests\StoreSubscriptionUpdateRequest;
use App\Http\Requests\RejectClaimRequest;
use App\Http\Requests\RequestClaimInfoRequest;
use App\Models\StoreBusinessClaim;
use App\Models\FileDetail;
use App\Models\Store;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Http\Requests\ValidateStoreRequest;
use Illuminate\Http\Request;
use App\Services\StoreService;
use App\Services\StoreBusinessClaimService;
use App\Services\Api\VikingStoreSystemService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\View\View;
use App\Models\Import;
use Nette\Schema\ValidationException;
use Symfony\Component\HttpFoundation\Response;

/**
 * StoreController
 *
 * Controller for handling store-related HTTP requests.
 */
class StoreController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @param StoreService $storeService
     * @param StoreBusinessClaimService $storeBusinessClaimService
     * @param VikingStoreSystemService $vikingStoreSystemService
     * @return void
     */
    public function __construct(
        protected StoreService $storeService,
        protected StoreBusinessClaimService $storeBusinessClaimService,
        protected VikingStoreSystemService $vikingStoreSystemService
    )
    {

    }

    /**
     * Display a listing of stores with optional filtering.
     *
     * @param StoreIndexRequest $request
     * @param string|null $type
     * @return View|JsonResponse
     */

    public function index(StoreIndexRequest $request, string $type = null)
    {
        $filters = $request->validated();
        $filterOptions = $this->storeService->getFilterOptions();
        $data = $this->storeService->getFilteredData($type, $filters, $request);

        if ($request->ajax()) {
            return response()->json([
                'data' => $data->items(),
                'current_page' => $data->currentPage(),
                'total' => $data->total(),
                'per_page' => $data->perPage(),
                'type' => $type
            ]);
        }

        return view('store.index', array_merge(
            [
                'data' => $data,
                'type' => $type
            ],
            $filterOptions
        ));
    }

    /**
     * Display a listing of store business claims with optional filtering.
     *
     * @param Request $request
     * @return View|JsonResponse
     */
    public function listOfStoreBusinessClaims(Request $request)
    {
        $filters = $request->all();
        $filterOptions = $this->storeBusinessClaimService->getFilterOptions();
        $data = $this->storeBusinessClaimService->getFilteredData($filters);

        if ($request->ajax()) {
            return response()->json([
                'data' => $data->items(),
                'current_page' => $data->currentPage(),
                'total' => $data->total(),
                'per_page' => $data->perPage()
            ]);
        }

        return view('store.claim.index', array_merge(
            [
                'data' => $data,
                'filters' => $filters,
                'statusEnum' => StoreBusinessClaimStatus::class
            ],
            $filterOptions
        ));
    }

    /**
     * Get claim detail for view display
     *
     * @param StoreBusinessClaim $claim
     * @return View
     */
    public function getClaimDetail(StoreBusinessClaim $claim): View
    {
        try {
            $claim = $this->storeBusinessClaimService->getClaimDetail($claim);

            return view('store.claim.detail', compact('claim'));
        } catch (\Exception $e) {
            Log::error('Failed to get claim detail: ' . $e->getMessage());
            abort(Response::HTTP_INTERNAL_SERVER_ERROR, 'Failed to load claim details');
        }
    }

    /**
     * Update the status of a store.
     *
     * @param string $id
     * @param int $status
     * @return JsonResponse
     *
     * @see StoreStatus For the status enum values
     */
    public function updateStatus(string $id, int $status): JsonResponse
    {
        $result = $this->storeService->updateStatus($id, $status);

        if ($result) {
            return response()->json(['success' => true, 'message' => 'Store status was updated successfully']);
        }
        return response()->json(['success' => false, 'message' => 'Failed to update store status'], Response::HTTP_INTERNAL_SERVER_ERROR);
    }

    public function showImport()
    {
        return view('store.import');
    }

    /**
     * Show the form for creating a new store.
     */
    public function create(): View
    {
        $metaData = $this->vikingStoreSystemService->getStoreMasterData();
        $cities = $this->storeService->getCities();
        $subscriptionPlans = $this->storeService->getSubscriptionPlans();

        return view('store.create', compact('metaData', 'cities', 'subscriptionPlans'));
    }

    /**
     * Store a newly created store in storage.
     */
    public function store(StoreCreateRequest $request): JsonResponse
    {
        try {
            $validatedData = $request->validated();
            $result = $this->storeService->createStore($validatedData);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Le magasin a été créé avec succès.',
                    'subscription_plan' => $validatedData['subscription_plan']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'] ?? 'La création du magasin a échoué.',
                    'errors' => $result['errors'] ?? []
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            Log::error('Store creation failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'La création du magasin a échoué. Veuillez réessayer.',
                'error' => $e->getMessage()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function validateStore(ValidateStoreRequest $request)
    {
        try {
            $file = $request->file('import_file');
            $result = $this->storeService->validateStore($file);
            $import = $this->storeService->getImportByBatchId($result['data']['batch_id']);

            if ($import->status_import === Import::STATUS_IMPORT_SUCCESS) {
                if ($import->invalid_count != 0) {
                    return [
                        'valid' => false,
                        'html' => view('store.modal.invalid_record', ['data' => $import])->render()
                    ];
                }

                return [
                    'valid' => true,
                    'html' => view('store.modal.valid_record', ['data' => $import])->render()
                ];
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $import->message,
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            Log::error('Validate store failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Validate store failed. Please try again.',
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function import(ImportStoreRequest $request)
    {
        try {
            $data = $request->validated();
            $result = $this->storeService->import($data['import_file']);
            $import = $this->storeService->getImportByBatchId($result['data']['batch_id']);

            if ($import->status_import === Import::STATUS_IMPORT_SUCCESS) {
                return view('store.modal.success', ['data' => $import, 'valid_count' => $data['import_valid_count']])->render();
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $import->message,
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $e) {
            Log::error('Import store failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Import store failed. Please try again.',
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }

    public function processImport(Import $import)
    {
        $data = $this->storeService->processImport($import);

        return view('store.process.detail', [
            'data' => $data
        ]);
    }

    /**
     * Display individual store detail information
     *
     * @param string $id
     * @return View
     */
    public function detail(string $id): View
    {
        $store = $this->storeService->getStoreWithAllRelations($id);
        if (!$store) {
            abort(Response::HTTP_NOT_FOUND, 'Store not found');
        }

        $storeDetail = $this->vikingStoreSystemService->getStoreDetail($id);
        $storeServices = $this->vikingStoreSystemService->getStoreServices($id);
        $storeCategories = $this->vikingStoreSystemService->getStoreCategories($id);
        $metaData = $this->vikingStoreSystemService->getStoreMasterData();
        $cities = $this->storeService->getCities();
        $questions = $this->storeService->getStoreQuestionsByCategories($id);
        $questionKeyMap = $this->storeService->mapStoreQuestionsKey();
        $subscriptionPlansData = $this->processSubscriptionPlansForDropdown($store);

        return view('store.detail', [
            'store' => $store,
            'storeDetail' => $storeDetail,
            'storeServices' => $storeServices,
            'storeCategories' => $storeCategories,
            'allCategories' => $metaData['categories'] ?? [],
            'metaData' => $metaData,
            'cities' => $cities,
            'storeId' => $id,
            'questions' => $questions,
            'questionKeyMap' => $questionKeyMap,
            'subscriptionPlansData' => $subscriptionPlansData,
        ]);
    }

    /**
     * Display the store schedules management interface
     * @return View
     */
    public function schedules(): View
    {
        try {
            $metaData = $this->vikingStoreSystemService->getStoreMasterData();

            return view('store.schedules', [
                'metaData' => $metaData,
                'pagination' => null,
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to load store schedules: ' . $e->getMessage());

            return view('store.schedules', [
                'metaData' => [],
                'pagination' => null,
                'data' => null
            ]);
        }
    }

    /**
     * Get stores data for schedules page via AJAX
     *
     * @param GetStoresDataRequest $request
     * @return JsonResponse
     */
    public function getStoresData(GetStoresDataRequest $request): JsonResponse
    {
        if ($request->query('filter_options')) {
            return $this->getFilterOptions();
        }

        return $this->getFilteredStoresData($request->validated());
    }

    /**
     * Get filter options (cities)
     *
     * @return JsonResponse
     */
    private function getFilterOptions(): JsonResponse
    {
        try {
            $cities = $this->vikingStoreSystemService->getAvailableCities();

            return response()->json([
                'success' => true,
                'cities' => $cities
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get filter options: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to load filter options',
                'cities' => []
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function scheduleTemplate()
    {
        $data = $this->storeService->getScheduleTemplate();

        return view('store.modal.template-schedule', [
            'data' => $data
        ]);
    }

    public function save(StoreScheduleRequest $request)
    {
        $data = $request->validated();
        $response = $this->storeService->saveSchedule($data);

        if ($response['status']) {
            return response()->json([
                'status' => true,
                'message' => $response['message'],
                'redirect_url' => route('store.schedules')
            ]);
        }

        return response()->json([
            'status' => false,
            'message' => $response['message'],
            'errors' => $response['errors'],
        ], Response::HTTP_BAD_REQUEST);
    }

    public function saveBulk(StoreScheduleBulkRequest $request)
    {
        $data = $request->validated();
        $response = $this->storeService->saveBulkSchedule($data);

        if ($response['status']) {
            return response()->json([
                'status' => true,
                'message' => $response['message'],
                'redirect_url' => route('store.schedules')
            ]);
        }

        return response()->json([
            'status' => false,
            'message' => $response['message'],
            'errors' => $response['errors'],
        ], Response::HTTP_BAD_REQUEST);
    }

    /**
     * Update store basic information
     *
     * @param StoreUpdateRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function updateBasicInfo(StoreUpdateRequest $request, string $id): JsonResponse
    {
        try {
            $data = $request->validated();
            $response = $this->vikingStoreSystemService->updateBasicInfo($id, $data);

            return $this->processApiResponse(
                $response,
                'Informations de base mises à jour avec succès',
                'Échec de la mise à jour des informations de base'
            );

        } catch (\Exception $e) {
            return $this->handleException(
                $e,
                'Error updating store basic info in controller',
                'Une erreur s\'est produite lors de la mise à jour du magasin'
            );
        }
    }

    /**
     * Update store categories
     *
     * @param StoreCategoriesUpdateRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function updateCategories(StoreCategoriesUpdateRequest $request, string $id): JsonResponse
    {
        try {
            $data = $request->validated();

            $apiData = [
                'ids' => $data['ids'] ?? []
            ];

            $response = $this->vikingStoreSystemService->updateCategories($id, $apiData);

            return $this->processApiResponse(
                $response,
                'Catégories mises à jour avec succès',
                'Échec de la mise à jour des catégories'
            );

        } catch (\Exception $e) {
            return $this->handleException(
                $e,
                'Error updating store categories in controller',
                'Une erreur s\'est produite lors de la mise à jour des catégories'
            );
        }
    }

    /**
     * Update store photos
     *
     * @param StorePhotosUpdateRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function updatePhotos(StorePhotosUpdateRequest $request, string $id): JsonResponse
    {
        try {
            Log::info('Photo upload request received', [
                'files' => $request->allFiles(),
                'inputs' => $request->all(),
                'store_id_param' => $id
            ]);

            $data = $request->validated();

            $image = $request->file('Image');
            $resizeImage = $request->file('ResizeImage');
            $storeId = $request->input('StoreId');
            $isPrimary = $request->boolean('IsPrimary');

            $response = $this->vikingStoreSystemService->addPhoto($storeId, $image, $resizeImage, $isPrimary);

            return $this->processApiResponse(
                $response,
                'Photo téléchargée avec succès',
                'Échec du téléchargement de la photo'
            );

        } catch (ValidationException $e) {
            Log::error('Validation error in photo upload: ', $e->errors());
            return response()->json([
                'success' => false,
                'message' => 'Erreur de validation',
                'errors' => $e->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Exception $e) {
            return $this->handleException(
                $e,
                'Error updating store photos in controller',
                'Une erreur s\'est produite lors de la mise à jour des photos'
            );
        }
    }

    /**
     * Delete store photo
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function deletePhoto(Request $request): JsonResponse
    {
        try {
            $storeId = $request->input('storeId');
            $imageId = $request->input('imageId');

            if (!$storeId || !$imageId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store ID et Image ID sont requis',
                    'errors' => ['Missing required parameters']
                ], Response::HTTP_BAD_REQUEST);
            }

            $response = $this->vikingStoreSystemService->deletePhoto($storeId, $imageId);

            if ($response['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $response['message'],
                    'data' => $response['data'] ?? null
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $response['message'],
                    'errors' => $response['errors'] ?? []
                ], Response::HTTP_BAD_REQUEST);
            }

        } catch (\Exception $e) {
            Log::error('Error deleting store photo in controller: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Une erreur s\'est produite lors de la suppression de la photo',
                'errors' => [$e->getMessage()]
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Replace store photo
     *
     * @param StorePhotosUpdateRequest $request
     * @return JsonResponse
     */
    public function replacePhoto(StorePhotosUpdateRequest $request): JsonResponse
    {
        try {
            $image = $request->file('Image');
            $resizeImage = $request->file('ResizeImage');
            $storeId = $request->input('StoreId');
            $imageId = $request->input('ImageId');
            $isPrimary = $request->boolean('IsPrimary');

            if (!$imageId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Image ID est requis pour remplacer une photo',
                    'errors' => ['Missing ImageId parameter']
                ], Response::HTTP_BAD_REQUEST);
            }

            $response = $this->vikingStoreSystemService->replacePhoto($storeId, $imageId, $image, $resizeImage, $isPrimary);

            if ($response['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $response['message'],
                    'data' => $response['data'] ?? null
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $response['message'],
                    'errors' => $response['errors'] ?? []
                ], Response::HTTP_BAD_REQUEST);
            }

        } catch (\Exception $e) {
            Log::error('Error replacing store photo in controller: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Une erreur s\'est produite lors du remplacement de la photo',
                'errors' => [$e->getMessage()]
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update store social networks
     *
     * @param StoreSocialUpdateRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function updateSocial(StoreSocialUpdateRequest $request, string $id): JsonResponse
    {
        try {
            $data = $request->validated();
            $response = $this->vikingStoreSystemService->updateSocial($id, $data);

            return $this->processApiResponse(
                $response,
                'Réseaux sociaux mis à jour avec succès',
                'Échec de la mise à jour des réseaux sociaux'
            );

        } catch (\Exception $e) {
            return $this->handleException(
                $e,
                'Error updating store social networks in controller',
                'Une erreur s\'est produite lors de la mise à jour des réseaux sociaux'
            );
        }
    }

    /**
     * Update store schedules
     *
     * @param StoreSchedulesUpdateRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function updateSchedules(StoreSchedulesUpdateRequest $request, string $id): JsonResponse
    {
        try {
            $data = $request->validated();

            $response = $this->vikingStoreSystemService->updateSchedules($id, $data);

            if ($response['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $response['message'],
                    'data' => $response['data'] ?? null
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $response['message'],
                    'errors' => $response['errors'] ?? []
                ], Response::HTTP_BAD_REQUEST);
            }

        } catch (\Exception $e) {
            Log::error('Error updating store schedules in controller: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Une erreur s\'est produite lors de la mise à jour des horaires',
                'errors' => [$e->getMessage()]
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update store description
     *
     * @param StoreDescriptionUpdateRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function updateDescription(StoreDescriptionUpdateRequest $request, string $id): JsonResponse
    {
        try {
            $data = $request->validated();
            dd($data);

            $response = $this->vikingStoreSystemService->updateDescription($id, $data);

            if ($response['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $response['message'],
                    'data' => $response['data'] ?? null
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $response['message'],
                    'errors' => $response['errors'] ?? []
                ], Response::HTTP_BAD_REQUEST);
            }

        } catch (\Exception $e) {
            Log::error('Error updating store description in controller: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Une erreur s\'est produite lors de la mise à jour de la description',
                'errors' => [$e->getMessage()]
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update store services
     *
     * @param StoreServicesUpdateRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function updateServices(StoreServicesUpdateRequest $request, string $id): JsonResponse
    {
        try {
            $data = $request->validated();
            $response = $this->vikingStoreSystemService->updateServices($id, $data);

            return $this->processApiResponse(
                $response,
                'Services mis à jour avec succès',
                'Échec de la mise à jour des services'
            );

        } catch (\Exception $e) {
            return $this->handleException(
                $e,
                'Error updating store services in controller',
                'Une erreur s\'est produite lors de la mise à jour des services'
            );
        }
    }

    /**
     * Update store additional information/answers
     *
     * @param StoreAnswersUpdateRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function updateAnswers(StoreAnswersUpdateRequest $request, string $id): JsonResponse
    {
        try {
            $data = $request->validated();
            $response = $this->vikingStoreSystemService->updateAnswers($id, $data);

            return $this->processApiResponse(
                $response,
                'Informations supplémentaires mises à jour avec succès',
                'Échec de la mise à jour des informations supplémentaires'
            );

        } catch (\Exception $e) {
            return $this->handleException(
                $e,
                'Error updating store answers in controller',
                'Une erreur s\'est produite lors de la mise à jour des informations supplémentaires'
            );
        }
    }

    /**
     * Update store subscription plan
     *
     * @param StoreSubscriptionUpdateRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function updateSubscription(StoreSubscriptionUpdateRequest $request, string $id): JsonResponse
    {
        try {
            $data = $request->validated();
            $subscriptionPlan = SubscriptionPlan::where('PlanType', $data['plan'])->first();

            if (!$subscriptionPlan) {
                return response()->json([
                    'success' => false,
                    'message' => 'Plan d\'abonnement non trouvé'
                ], Response::HTTP_BAD_REQUEST);
            }

            $store = $this->storeService->getStoreWithAllRelations($id);

            if (!$store) {
                return response()->json([
                    'success' => false,
                    'message' => 'Magasin non trouvé'
                ], Response::HTTP_NOT_FOUND);
            }

            $isNoPlan = (int) $data['plan'] === SubscriptionLevel::BASIC_TYPE->value;

            if ($isNoPlan) {
                if ($store->subscription) {
                    $store->subscription->delete();
                }
            } else {
                if ($store->subscription) {
                    $store->subscription->update([
                        'SubscriptionPlanId' => $subscriptionPlan->Id,
                        'ModifiedDate' => now()
                    ]);
                } else {
                    Subscription::create([
                        'Id' => Str::uuid()->toString(),
                        'StoreId' => $store->Id,
                        'SubscriptionPlanId' => $subscriptionPlan->Id,
                        'StartTime' => now(),
                        'EndTime' => now()->addYear(),
                        'State' => 0,
                        'CreatedDate' => now(),
                        'ModifiedDate' => now(),
                        'IsDeleted' => 0
                    ]);
                }
            }

            $data = [
                'plan' => (int) $subscriptionPlan->Id
            ];

            $response = $this->vikingStoreSystemService->updateSubscription($id, $data);

            return $this->processApiResponse(
                $response,
                'Plan d\'abonnement mis à jour avec succès',
                'Échec de la mise à jour du plan d\'abonnement'
            );

        } catch (\Exception $e) {
            \Log::error('Error in updateSubscription', [
                'store_id' => $id,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->handleException(
                $e,
                'Error updating store subscription in controller',
                'Une erreur s\'est produite lors de la mise à jour du plan d\'abonnement'
            );
        }
    }

    /**
     * @param Store $store
     * @return array
     */
    private function processSubscriptionPlansForDropdown(Store $store): array
    {
        $subscriptionPlans = SubscriptionPlan::orderBy('PlanType', 'asc')->get();
        $currentPlanId = $store->subscriptionPlan->Id ?? null;
        $currentPlanName = $store->subscriptionPlan->Name ?? 'No Plan';

        if ($currentPlanName === 'Basic Free Plan') {
            $currentPlanName = 'No Plan';
        }

        $availablePlans = [];
        $planStyles = [
            'No Plan' => ['color' => '#dc3545', 'bg' => '#f8d7da', 'border' => '#f5c6cb', 'icon' => 'fas fa-times-circle'],
            'Bronze' => ['color' => '#8B4513', 'bg' => '#f4e4d6', 'border' => '#d4a574', 'icon' => 'fas fa-medal'],
            'Silver' => ['color' => '#6c757d', 'bg' => '#f8f9fa', 'border' => '#dee2e6', 'icon' => 'fas fa-medal'],
            'Gold' => ['color' => '#ffc107', 'bg' => '#fff3cd', 'border' => '#ffeaa7', 'icon' => 'fas fa-crown'],
            'Platinum' => ['color' => '#007bff', 'bg' => '#e3f2fd', 'border' => '#bbdefb', 'icon' => 'fas fa-gem']
        ];

        foreach ($subscriptionPlans as $plan) {
            $planDisplayName = $plan->Name === 'Basic Free Plan' ? 'No Plan' : $plan->Name;
            $shouldShow = true;

            if ($plan->Id == $currentPlanId) {
                $shouldShow = false;
            }

            if ($currentPlanName === 'No Plan' && $planDisplayName === 'No Plan') {
                $shouldShow = false;
            }

            if ($shouldShow) {
                $style = $planStyles[$planDisplayName] ?? ['color' => '#6c757d', 'bg' => '#f8f9fa', 'border' => '#dee2e6', 'icon' => 'fas fa-star'];

                $availablePlans[] = [
                    'id' => $plan->Id,
                    'planType' => $plan->PlanType,
                    'name' => $plan->Name,
                    'displayName' => $planDisplayName,
                    'style' => $style
                ];
            }
        }

        return [
            'currentPlanId' => $currentPlanId,
            'currentPlanName' => $currentPlanName,
            'availablePlans' => $availablePlans
        ];
    }

    /**
     * Export a CSV template with headers for store import.
     *
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function exportCsvTemplate()
    {
        $headers = [
            'CompanyName', 'NameOfTheBusiness', 'ContactPhoneNumber', 'ContactEmail',
            'Address1', 'Plan', 'Address2', 'City', 'ZipCode', 'NameLegalResponsible',
            'SirenNumber', 'StorePhoneNumber', 'CodeApeNaf', 'Category', 'SubCategory',
            'Website', 'Instagram', 'Facebook', 'LinkedIn', 'XTwitter', 'Description',
            'Services', 'Facilities', 'Accessibility', 'Ambiance Type', 'Spoken Languages',
            'MondayMorningOpen', 'MondayMorningClose', 'TuesdayMorningOpen', 'TuesdayMorningClose',
            'WednesdayMorningOpen', 'WednesdayMorningClose', 'ThursdayMorningOpen', 'ThursdayMorningClose',
            'FridayMorningOpen', 'FridayMorningClose', 'SaturdayMorningOpen', 'SaturdayMorningClose',
            'SundayMorningOpen', 'SundayMorningClose', 'MondayAfternoonOpen', 'MondayAfternoonClose',
            'TuesdayAfternoonOpen', 'TuesdayAfternoonClose', 'WednesdayAfternoonOpen', 'WednesdayAfternoonClose',
            'ThursdayAfternoonOpen', 'ThursdayAfternoonClose', 'FridayAfternoonOpen', 'FridayAfternoonClose',
            'SaturdayAfternoonOpen', 'SaturdayAfternoonClose', 'SundayAfternoonOpen', 'SundayAfternoonClose',
            'LoyaltyWithholdingOption', 'Longitude', 'Latitude'
        ];

        $descriptions = [
            'Name of Store', 'Name of the Company', 'Phone number of Contact person', 'Email',
            'Adresss 1', "Subscription Plan with value:\nNEW\nBRONZE\nSILVER\nGOLD\nPLATINUM", 'Adresss 2', 'City', 'Zip Code', 'Name of Representative',
            'French business identification number', 'Phone number of the Store', 'Standardized business classification code used in France', 'Category', 'Sub Category',
            'Link of Website', 'Link of Instagram', 'Link of Facebook', 'Link of LinkedIn', 'Link of X (Twitter)', 'Description of Store',
            'Services categorized', 'Facilities', 'Accessibility', 'Type of Atmosphere', 'Spoken Languages',
            'Open Hour: HH:MM', 'Close Hour: HH:MM', 'Open Hour: HH:MM', 'Close Hour: HH:MM',
            'Open Hour: HH:MM', 'Close Hour: HH:MM', 'Open Hour: HH:MM', 'Close Hour: HH:MM',
            'Open Hour: HH:MM', 'Close Hour: HH:MM', 'Open Hour: HH:MM', 'Close Hour: HH:MM',
            'Open Hour: HH:MM', 'Close Hour: HH:MM', 'Open Hour: HH:MM', 'Close Hour: HH:MM',
            'Open Hour: HH:MM', 'Close Hour: HH:MM', 'Open Hour: HH:MM', 'Close Hour: HH:MM',
            'Open Hour: HH:MM', 'Close Hour: HH:MM', 'Open Hour: HH:MM', 'Close Hour: HH:MM',
            'Open Hour: HH:MM', 'Close Hour: HH:MM', 'Open Hour: HH:MM', 'Close Hour: HH:MM',
            null,
            "Measures how far north or south a location is from the equator (0° latitude).\nRanges from -180° to +180°",
            "Measures how far east or west a location is from the Prime Meridian (0° longitude, which passes through Greenwich, England).\nRanges from -90° to +90°"
        ];

        $required = [
            'Required','Required','Required','Required','Required','Required','Optional','Required','Required','Required','Required','Optional','Optional','Required','Required','Optional','Optional','Optional','Optional','Optional','Optional','Optional','Optional','Optional','Optional','Optional',
            'Required when Close Hour in that day has a certain value','Required when Open Hour in that day has a certain value','Required when Close Hour in that day has a certain value','Required when Open Hour in that day has a certain value','Required when Close Hour in that day has a certain value','Required when Open Hour in that day has a certain value','Required when Close Hour in that day has a certain value','Required when Open Hour in that day has a certain value','Required when Close Hour in that day has a certain value','Required when Open Hour in that day has a certain value','Required when Close Hour in that day has a certain value','Required when Open Hour in that day has a certain value','Required when Close Hour in that day has a certain value','Required when Open Hour in that day has a certain value','Optional','Optional','Optional','Optional','Optional','Optional','Optional','Optional','Optional','Optional','Optional','Optional','Optional','Optional','Optional','Required','Required'
        ];

        $instructions = [
            '# INSTRUCTIONS:',
            '# Please read carefully before filling out the file.',
            '# 1. Do NOT modify the column headers (Row 1). Keep them exactly as provided.',
            '# 2. All fields marked "Required" in Row 3 must be filled in.',
            '# 3. For open/close hours (e.g., MondayMorningOpen), provide both open and close times if one is filled. Format must be HH:MM (e.g., 09:00).',
            '# 4. Longitude must range from -180 to +180. Latitude must range from -90 to +90. Use decimal format (e.g., 48.8566).',
            '# 5. Plan must be one of: NEW, BRONZE, SILVER, GOLD, PLATINUM.',
            '# 6. If you don\'t have information for optional fields, you may leave them blank.',
            '# 7. Save the file as CSV UTF-8 before uploading.',
            '# 8. Upload your file through the admin portal under the "Importer des magasins" section.',
            '# If your file contains errors, the system will flag them with detailed row/column messages for correction.'
        ];

        $filename = 'store_import_template_' . date('Y-m-d') . '.csv';

        return response()->streamDownload(function() use ($headers, $descriptions, $required, $instructions) {
            $output = fopen('php://output', 'w');

            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
            fputcsv($output, $headers);
            fputcsv($output, $descriptions);
            fputcsv($output, $required);
            // Add empty row
            fputcsv($output, array_fill(0, count($headers), ''));
            // Add instructions as quoted comment lines (to avoid comma issues)
            foreach ($instructions as $line) {
                $quoted = '"' . str_replace('"', '""', $line) . '"';
                fwrite($output, $quoted . str_repeat(',', count($headers) - 1) . "\n");
            }
            fclose($output);
        }, $filename, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ]);
    }

    /**
     * Get filtered schedules data
     *
     * @param GetStoresDataRequest $request
     * @return JsonResponse
     */
    public function getFilteredSchedules(GetStoresDataRequest $request): JsonResponse
    {
        return $this->getFilteredStoresData($request->validated());
    }

    /**
     * Common method to get filtered stores data
     *
     * @param array $validatedData
     * @return JsonResponse
     */
    private function getFilteredStoresData(array $validatedData): JsonResponse
    {
        try {
            $page = $validatedData['page'] ?? 1;
            $perPage = $validatedData['per_page'] ?? 25;
            $search = $validatedData['search'] ?? '';
            $cities = $validatedData['cities'] ?? '';

            $citiesArray = !empty($cities) ? explode(',', $cities) : [];

            $storesResult = $this->vikingStoreSystemService->getAllStoresWithFilters(
                $search,
                $citiesArray,
                $page,
                $perPage
            );

            return response()->json([
                'success' => true,
                'data' => $storesResult['data'] ?? [],
                'pagination' => $storesResult['pagination'] ?? null
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to get stores data: " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => "Failed to load stores data",
                'data' => [],
                'pagination' => null
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Delete a store
     *
     * @param string $id
     * @return JsonResponse
     */
    public function deleteStore(string $id): JsonResponse
    {
        try {
            $response = $this->vikingStoreSystemService->deleteStore($id);

            return $this->processApiResponse(
                $response,
                'Magasin supprimé avec succès',
                'Échec de la suppression du magasin'
            );

        } catch (\Exception $e) {
            return $this->handleException($e, 'Store deletion failed', 'Failed to delete store');
        }
    }

    /**
     * Handle successful API response
     *
     * @param array $response
     * @param string|null $defaultMessage
     * @return JsonResponse
     */
    private function handleSuccessResponse(array $response, string $defaultMessage = null): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $response['message'] ?? $defaultMessage ?? 'Operation completed successfully',
            'data' => $response['data'] ?? null
        ]);
    }

    /**
     * Handle failed API response
     *
     * @param array $response
     * @param string|null $defaultMessage
     * @return JsonResponse
     */
    private function handleFailedResponse(array $response, string $defaultMessage = null): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $response['message'] ?? $defaultMessage ?? 'Operation failed',
            'errors' => $response['errors'] ?? []
        ], Response::HTTP_BAD_REQUEST);
    }

    /**
     * Handle exceptions with consistent logging and response
     *
     * @param \Exception $e
     * @param string $logMessage
     * @param string $userMessage
     * @return JsonResponse
     */
    private function handleException(\Exception $e, string $logMessage, string $userMessage): JsonResponse
    {
        Log::error($logMessage . ': ' . $e->getMessage());

        return response()->json([
            'success' => false,
            'message' => $userMessage,
            'errors' => [$e->getMessage()]
        ], Response::HTTP_INTERNAL_SERVER_ERROR);
    }

    /**
     * Process API response and return appropriate JSON response
     *
     * @param array $response
     * @param string $successMessage
     * @param string $failureMessage
     * @return JsonResponse
     */
    private function processApiResponse(array $response, string $successMessage, string $failureMessage): JsonResponse
    {
        if ($response['success']) {
            return $this->handleSuccessResponse($response, $successMessage);
        } else {
            return $this->handleFailedResponse($response, $failureMessage);
        }
    }

    /**
     * Download document from filedetails
     *
     * @param FileDetail $fileDetail
     * @return Response|JsonResponse
     */
    public function downloadClaimDocument(FileDetail $fileDetail): JsonResponse|Response
    {
        try {
            if ($fileDetail->IsDeleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document not found'
                ], Response::HTTP_NOT_FOUND);
            }

            $apiResponse = $this->vikingStoreSystemService->downloadFile($fileDetail->Id);

            if ($apiResponse['success'] && isset($apiResponse['file_content'])) {
                $fileName = $apiResponse['is_binary'] ? $fileDetail->FileName : ($apiResponse['file_name'] ?? $fileDetail->FileName);
                $fileType = $apiResponse['file_type'] ?? 'application/octet-stream';

                return response($apiResponse['file_content'])
                    ->header('Content-Type', $fileType)
                    ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"');
            }

            return response()->json([
                'success' => false,
                'message' => $apiResponse['message'] ?? 'Failed to download document from server'
            ], Response::HTTP_BAD_REQUEST);

        } catch (\Exception $e) {
            Log::error("Failed to download claim document: " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to download document'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * View document from filedetails
     *
     * @param FileDetail $fileDetail
     * @return Response|JsonResponse
     */
    public function viewClaimDocument(FileDetail $fileDetail): JsonResponse|Response
    {
        try {
            if ($fileDetail->IsDeleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Document not found'
                ], Response::HTTP_NOT_FOUND);
            }

            $apiResponse = $this->vikingStoreSystemService->downloadFile($fileDetail->Id);

            if ($apiResponse['success'] && isset($apiResponse['file_content'])) {
                $fileName = $apiResponse['is_binary'] ? $fileDetail->FileName : ($apiResponse['file_name'] ?? $fileDetail->FileName);
                $fileType = $apiResponse['file_type'] ?? 'application/octet-stream';

                return response($apiResponse['file_content'])
                    ->header('Content-Type', $fileType)
                    ->header('Content-Disposition', 'inline; filename="' . $fileName . '"');
            }

            return response()->json([
                'success' => false,
                'message' => $apiResponse['message'] ?? 'Failed to view document from server'
            ], Response::HTTP_BAD_REQUEST);

        } catch (\Exception $e) {
            Log::error('Failed to view document from server: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to view document'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Approve a store business claim
     *
     * @param StoreBusinessClaim $claim
     * @return JsonResponse
     */
    public function approveClaim(StoreBusinessClaim $claim): JsonResponse
    {
        try {
            $result = $this->storeBusinessClaimService->approveClaim($claim);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'La revendication a été approuvée avec succès.',
                    'status' => StoreBusinessClaimStatus::APPROVED->value
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Échec de l\'approbation de la revendication.'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);

        } catch (\Exception $e) {
            Log::error('Failed to approve claim: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de l\'approbation de la revendication.'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Reject a store business claim
     *
     * @param RejectClaimRequest $request
     * @param StoreBusinessClaim $claim
     * @return JsonResponse
     */
    public function rejectClaim(RejectClaimRequest $request, StoreBusinessClaim $claim): JsonResponse
    {
        try {
            $data = $request->validated();
            $result = $this->storeBusinessClaimService->rejectClaim($claim, $data);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'La revendication a été rejetée avec succès.',
                    'status' => StoreBusinessClaimStatus::REJECTED->value,
                    'reject_reason' => $data['reject_reason']
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Échec du rejet de la revendication.'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);

        } catch (\Exception $e) {
            Log::error('Failed to reject claim: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors du rejet de la revendication.'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Request additional information for a store business claim
     *
     * @param RequestClaimInfoRequest $request
     * @param StoreBusinessClaim $claim
     * @return JsonResponse
     */
    public function requestClaimInfo(RequestClaimInfoRequest $request, StoreBusinessClaim $claim): JsonResponse
    {
        try {
            $data = $request->validated();
            $result = $this->storeBusinessClaimService->requestClaimInfo($claim, $data);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'La demande d\'informations supplémentaires a été envoyée avec succès.',
                    'status' => StoreBusinessClaimStatus::INFO_REQUESTED->value
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Échec de l\'envoi de la demande d\'informations.'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);

        } catch (\Exception $e) {
            Log::error('Failed to request claim info: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de l\'envoi de la demande d\'informations.'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get exceptional hours for a store
     *
     * @param string $storeId
     * @param Request $request
     * @return JsonResponse
     */
    public function getExceptionalHours(string $storeId, Request $request): JsonResponse
    {
        try {
            $filters = $request->all();
            $data = $this->vikingStoreSystemService->getHolidayEvents($storeId, $filters);

            return response()->json([
                'success' => true,
                'data' => $data
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get exceptional hours: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Une erreur s\'est produite lors du chargement des horaires exceptionnels',
                'data' => ['items' => [], 'metadata' => ['totalItem' => 0, 'pageSize' => 10, 'page' => 1]]
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get exceptional hours detail
     *
     * @param string $storeId
     * @param string $eventId
     * @return JsonResponse
     */
    public function getExceptionalHoursDetail(string $storeId, string $eventId): JsonResponse
    {
        try {
            $data = $this->vikingStoreSystemService->getHolidayEventDetail($eventId);

            return response()->json([
                'success' => true,
                'data' => $data
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get exceptional hours detail: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Une erreur s\'est produite lors du chargement des détails de l\'événement'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Create exceptional hours
     *
     * @param string $storeId
     * @param Request $request
     * @return JsonResponse
     */
    public function createExceptionalHours(string $storeId, Request $request): JsonResponse
    {
        try {
            $data = $request->all();

            $data['storeId'] = $storeId;

            $result = $this->vikingStoreSystemService->createHolidayEvent($data);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Failed to create exceptional hours: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Une erreur s\'est produite lors de la création de l\'événement',
                'errors' => [$e->getMessage()]
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update exceptional hours
     *
     * @param string $storeId
     * @param string $eventId
     * @param Request $request
     * @return JsonResponse
     */
    public function updateExceptionalHours(string $storeId, string $eventId, Request $request): JsonResponse
    {
        try {
            $data = $request->all();

            $data['storeId'] = $storeId;

            $result = $this->vikingStoreSystemService->updateHolidayEvent($eventId, $data);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Failed to update exceptional hours: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Une erreur s\'est produite lors de la mise à jour de l\'événement',
                'errors' => [$e->getMessage()]
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update exceptional hours status
     *
     * @param string $storeId
     * @param string $eventId
     * @param Request $request
     * @return JsonResponse
     */
    public function updateExceptionalHoursStatus(string $storeId, string $eventId, Request $request): JsonResponse
    {
        try {
            $status = $request->input('status');

            if (!in_array($status, [0, 1])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Statut invalide'
                ], Response::HTTP_BAD_REQUEST);
            }

            $result = $this->vikingStoreSystemService->updateHolidayEventStatus($eventId, $status);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Failed to update exceptional hours status: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Une erreur s\'est produite lors de la mise à jour du statut',
                'errors' => [$e->getMessage()]
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Delete exceptional hours
     *
     * @param string $storeId
     * @param string $eventId
     * @return JsonResponse
     */
    public function deleteExceptionalHours(string $storeId, string $eventId): JsonResponse
    {
        try {
            $result = $this->vikingStoreSystemService->deleteHolidayEvent($eventId);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Failed to delete exceptional hours: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Une erreur s\'est produite lors de la suppression de l\'événement',
                'errors' => [$e->getMessage()]
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
