<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'string|max:255',
            'addressLine1' => 'string|max:255',
            'addressLine2' => 'nullable|string|max:255',
            'phoneNumber' => 'string|max:255',
            'city' => 'string|max:100',
            'zipCode' => 'string|max:20',
            'legalOfficer' => 'nullable|string|max:255',
            'companyRegistrationCode' => 'nullable|string|max:50',
            'apeCode' => 'nullable|string|max:20',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'email' => 'email|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'Le nom de l\'entreprise est requis',
            'name.max' => 'Le nom de l\'entreprise ne peut pas dépasser 255 caractères',
            'addressLine1.required' => 'L\'adresse principale est requise',
            'addressLine1.max' => 'L\'adresse principale ne peut pas dépasser 255 caractères',
            'addressLine2.max' => 'L\'adresse secondaire ne peut pas dépasser 255 caractères',
            'city.required' => 'La ville est requise',
            'city.max' => 'La ville ne peut pas dépasser 100 caractères',
            'zipCode.required' => 'Le code postal est requis',
            'zipCode.max' => 'Le code postal ne peut pas dépasser 20 caractères',
            'legalOfficer.max' => 'Le nom du directeur légal ne peut pas dépasser 255 caractères',
            'companyRegistrationCode.max' => 'Le numéro d\'enregistrement ne peut pas dépasser 50 caractères',
            'apeCode.max' => 'Le code APE ne peut pas dépasser 20 caractères',
            'latitude.numeric' => 'La latitude doit être un nombre',
            'latitude.between' => 'La latitude doit être comprise entre -90 et 90',
            'longitude.numeric' => 'La longitude doit être un nombre',
            'longitude.between' => 'La longitude doit être comprise entre -180 et 180',
        ];
    }
}
