<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreScheduleRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'stores' => ['required','array'],
            'stores.*' => ['array'],
            'stores.*.*' => ['array'],
            'stores.*.*.*' => ['array'],
            'stores.*.*.*.open' => ['nullable','date_format:H:i'],
            'stores.*.*.*.close' => ['nullable','date_format:H:i'],
        ];
    }
    public function withValidator($validator): void
    {
        $validator->after(function ($v) {
            foreach ($this->input('stores', []) as $uuid => $days) {
                foreach ($days as $day => $periods) {
                    foreach ($periods as $idx => $p) {

                        $open = $p['open']  ?? null;
                        $close = $p['close'] ?? null;

                        if (($open && !$close) || (!$open && $close)) {
                            $v->errors()->add(
                                "stores.$uuid.$day.$idx",
                                'Both open & close hours must be entered'
                            );
                        }

                        if ($open && $close && $open > $close) {
                            $v->errors()->add(
                                "stores.$uuid.$day.$idx",
                                'Opening hours must be less than or equal to closing hours'
                            );
                        }
                    }
                }
            }
        });
    }
}
