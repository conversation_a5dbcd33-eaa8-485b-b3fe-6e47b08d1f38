<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAnswersUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'model' => 'nullable|string',
            'minimumAge' => 'nullable|integer|min:0|max:100',
            'productManufacturingOrigins' => 'nullable|array',
            'productManufacturingOrigins.*' => 'integer',
            'localProducer' => 'nullable|integer|in:0,1',
            'typeOfCuisines' => 'nullable|array',
            'typeOfCuisines.*' => 'string',
            'averageBudgetPerPerson' => 'nullable|integer|min:0',
            'sellOnStreetMarket' => 'nullable|integer|in:0,1',
            'markets' => 'nullable|array',
            'markets.*.marketInformationId' => 'required_with:markets|string',
            'markets.*.dayOfWeeks' => 'required_with:markets|array',
            'markets.*.dayOfWeeks.*' => 'integer|between:0,6',
            'zeroWasteOffer' => 'nullable|integer|in:0,1',
            'partners' => 'nullable|array',
            'partners.*' => 'integer',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'minimumAge.integer' => 'L\'âge minimum doit être un nombre entier',
            'minimumAge.min' => 'L\'âge minimum ne peut pas être négatif',
            'minimumAge.max' => 'L\'âge minimum ne peut pas dépasser 100',
            'localProducer.in' => 'Le producteur local doit être 0 ou 1',
            'averageBudgetPerPerson.integer' => 'Le budget moyen doit être un nombre entier',
            'averageBudgetPerPerson.min' => 'Le budget moyen ne peut pas être négatif',
            'sellOnStreetMarket.in' => 'La vente sur marché doit être 0 ou 1',
            'zeroWasteOffer.in' => 'L\'offre zéro déchet doit être 0 ou 1',
        ];
    }
}
