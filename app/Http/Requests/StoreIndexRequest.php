<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */

    public function rules()
    {
        return [
            'name_or_email' => 'nullable|string',
            'city' => 'nullable|string',
            'subscription_plan' => 'nullable|string',
            'status' => 'nullable|string',
            'category' => 'nullable|string',
            'sort_field' => 'nullable|string|in:created_date,name,city,status,subscription_plan',
            'sort_direction' => 'nullable|string|in:asc,desc',
        ];
    }
}
