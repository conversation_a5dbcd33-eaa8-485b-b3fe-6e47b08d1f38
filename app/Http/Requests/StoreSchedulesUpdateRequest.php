<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSchedulesUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'addressId' => 'required|string',
            'hoursOfOperations' => 'required|array',
            'hoursOfOperations.*.dayOfWeek' => 'required|integer|between:0,6',
            'hoursOfOperations.*.morningOpenTime' => 'nullable|string',
            'hoursOfOperations.*.morningCloseTime' => 'nullable|string',
            'hoursOfOperations.*.afternoonOpenTime' => 'nullable|string',
            'hoursOfOperations.*.afternoonCloseTime' => 'nullable|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'addressId.required' => 'L\'ID d\'adresse est requis',
            'addressId.string' => 'L\'ID d\'adresse doit être une chaîne de caractères',
            'hoursOfOperations.required' => 'Les heures d\'ouverture sont requises',
            'hoursOfOperations.array' => 'Les heures d\'ouverture doivent être un tableau',
            'hoursOfOperations.*.dayOfWeek.required' => 'Le jour de la semaine est requis',
            'hoursOfOperations.*.dayOfWeek.integer' => 'Le jour de la semaine doit être un nombre entier',
            'hoursOfOperations.*.dayOfWeek.between' => 'Le jour de la semaine doit être entre 0 et 6',
        ];
    }
}
