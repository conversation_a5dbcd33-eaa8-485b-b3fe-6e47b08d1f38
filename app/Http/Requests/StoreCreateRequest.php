<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Services\StoreService;

class StoreCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'string|max:200',
            'company_name' => 'string|max:200',
            'phone' => 'string',
            'email' => 'email|max:255',
            'addressLine1' => 'string|max:255',
            'addressLine2' => 'nullable|string|max:255',
            'city' => 'string|max:100',
            'zipCode' => 'numeric|digits:5',
            'representative_name' => 'string|max:200',
            'siren' => 'string',
            'subscription_plan' => 'required',
            'main_categories' => 'nullable|array',
            'main_categories.*' => 'string',
            'subcategories' => 'nullable|array',
            'subcategories.*' => 'string',
            'latitude' => 'numeric|between:-90,90',
            'longitude' => 'numeric|between:-180,180',
            'store_username' => 'nullable|email|max:255',
            'store_password' => 'nullable|string|min:6|max:255',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $mainCategories = $this->input('main_categories', []);
            $subcategories = $this->input('subcategories', []);

            if (empty($mainCategories) && empty($subcategories)) {
                $validator->errors()->add('categories', 'Au moins une catégorie ou sous-catégorie doit être sélectionnée.');
            }

            $email = $this->input('email');
            if ($email) {
                $storeService = app(StoreService::class);
                if ($storeService->checkStoreExistsByEmail($email)) {
                    $validator->errors()->add('email', 'Un magasin avec cette adresse e-mail existe déjà.');
                }
            }

            $name = $this->input('name');
            $latitude = $this->input('latitude');
            $longitude = $this->input('longitude');

            if ($name && $latitude && $longitude) {
                $storeService = app(StoreService::class);
                if ($storeService->checkStoreExistsByLocation($name, (float)$latitude, (float)$longitude)) {
                    $validator->errors()->add('name', 'Un magasin avec ce nom et cette localisation existe déjà.');
                }
            }

            $siren = $this->input('siren');
            if ($siren) {
                $sirenDigits = preg_replace('/[^0-9]/', '', $siren);
                if (strlen($sirenDigits) !== 9 && strlen($sirenDigits) !== 14) {
                    $validator->errors()->add('siren', 'Le numéro SIREN/SIRET doit contenir exactement 9 chiffres (SIREN) ou 14 chiffres (SIRET).');
                }
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'Le nom du magasin est obligatoire.',
            'name.max' => 'Le nom du magasin ne peut pas dépasser 200 caractères.',
            'company_name.required' => 'Le nom de l\'entreprise est obligatoire.',
            'company_name.max' => 'Le nom de l\'entreprise ne peut pas dépasser 200 caractères.',
            'phone.required' => 'Le numéro de téléphone est obligatoire.',
            'phone.regex' => 'Le format du numéro de téléphone n\'est pas valide.',
            'email.required' => 'L\'adresse e-mail est obligatoire.',
            'email.email' => 'L\'adresse e-mail doit être valide.',
            'email.unique' => 'Cette adresse e-mail est déjà utilisée.',
            'addressLine1.required' => 'L\'adresse est obligatoire.',
            'city.required' => 'La ville est obligatoire.',
            'zipCode.required' => 'Le code postal est obligatoire.',
            'zipCode.numeric' => 'Le code postal doit être numérique.',
            'zipCode.digits' => 'Le code postal doit contenir exactement 5 chiffres.',
            'representative_name.required' => 'Le nom du représentant est obligatoire.',
            'representative_name.max' => 'Le nom du représentant ne peut pas dépasser 200 caractères.',
            'siren.required' => 'Le numéro SIREN est obligatoire.',
            'siren.regex' => 'Le numéro SIREN/SIRET doit contenir exactement 9 chiffres (SIREN) ou 14 chiffres (SIRET).',
            'subscription_plan.required' => 'Le plan d\'abonnement est obligatoire.',
            'subscription_plan.in' => 'Le plan d\'abonnement sélectionné n\'est pas valide.',
            'categories.required' => 'Au moins une catégorie doit être sélectionnée.',
            'categories.min' => 'Au moins une catégorie doit être sélectionnée.',
            'latitude.required' => 'La latitude est obligatoire.',
            'latitude.numeric' => 'La latitude doit être un nombre.',
            'latitude.between' => 'La latitude doit être comprise entre -90 et 90.',
            'longitude.required' => 'La longitude est obligatoire.',
            'longitude.numeric' => 'La longitude doit être un nombre.',
            'longitude.between' => 'La longitude doit être comprise entre -180 et 180.',
        ];
    }
}
