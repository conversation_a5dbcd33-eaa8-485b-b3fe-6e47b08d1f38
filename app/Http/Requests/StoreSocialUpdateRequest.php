<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSocialUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'siteInternet' => 'nullable|max:255',
            'instagram' => 'nullable|max:255',
            'facebook' => 'nullable|max:255',
            'linkedIn' => 'nullable|max:255',
            'xTwitter' => 'nullable|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'siteInternet.max' => 'L\'URL du site internet ne peut pas dépasser 255 caractères',
            'instagram.max' => 'L\'URL Instagram ne peut pas dépasser 255 caractères',
            'facebook.max' => 'L\'URL Facebook ne peut pas dépasser 255 caractères',
            'linkedIn.max' => 'L\'URL LinkedIn ne peut pas dépasser 255 caractères',
            'xTwitter.max' => 'L\'URL Twitter ne peut pas dépasser 255 caractères',
        ];
    }
}
