<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetStoresDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'filter_options' => [
                'nullable',
                'boolean'
            ],
            'page' => [
                'nullable',
                'integer',
                'min:1'
            ],
            'per_page' => [
                'nullable',
                'integer',
                'min:1',
                'max:100'
            ],
            'search' => [
                'nullable',
                'string',
                'max:255'
            ],
            'cities' => [
                'nullable',
                'string',
                'max:1000'
            ],
        ];
    }
}
