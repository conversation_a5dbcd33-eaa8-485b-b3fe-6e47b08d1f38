<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreServicesUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'serviceIds' => 'nullable|array',
            'serviceIds.*' => 'nullable|string',
            'paymentIds' => 'nullable|array',
            'paymentIds.*' => 'nullable|string',
            'languageIds' => 'nullable|array',
            'languageIds.*' => 'nullable|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'serviceIds.array' => 'Les services doivent être un tableau',
            'serviceIds.*.required' => 'Chaque service doit avoir un ID valide',
            'serviceIds.*.string' => 'L\'ID de service doit être une chaîne de caractères',
            'paymentIds.array' => 'Les méthodes de paiement doivent être un tableau',
            'paymentIds.*.required' => 'Chaque méthode de paiement doit avoir un ID valide',
            'paymentIds.*.string' => 'L\'ID de méthode de paiement doit être une chaîne de caractères',
            'languageIds.array' => 'Les langues doivent être un tableau',
            'languageIds.*.required' => 'Chaque langue doit avoir un ID valide',
            'languageIds.*.string' => 'L\'ID de langue doit être une chaîne de caractères',
        ];
    }
}
