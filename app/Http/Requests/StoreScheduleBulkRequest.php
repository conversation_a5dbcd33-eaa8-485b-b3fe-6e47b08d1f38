<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreScheduleBulkRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'template_id' => [
                'required',
                'exists:schedule_templates,id'
            ],
            'store_ids' => [
                'required',
                'array'
            ],
            'store_ids.*' => [
                'required',
                'exists:stores,id'
            ],
        ];
    }
}
