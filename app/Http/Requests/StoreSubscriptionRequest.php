<?php

namespace App\Http\Requests;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class StoreSubscriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        if ($this->date_renewal) {
            $dateRenewal = Carbon::createFromFormat('d/m/Y', $this->date_renewal);
            $this->merge([
                'date_renewal' => $dateRenewal->format('Y-m-d')
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'plan' => [
                'required',
                'exists:subscriptionplans,id'
            ],
            'date_purchase' => [
                'required',
                'date',
                'before:date_renewal'
            ],
            'date_renewal' => [
                'required',
                'date',
                'after:date_purchase'
            ],
        ];
    }
}