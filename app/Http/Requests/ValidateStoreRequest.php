<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ValidateStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'import_file' => 'required|file|mimes:csv,txt|max:10240',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'import_file.required' => 'Please select a file to import',
            'import_file.file' => 'The import must be a file',
            'import_file.mimes' => 'The file must be a CSV file',
            'import_file.max' => 'The file size must not exceed 10MB',
        ];
    }
}