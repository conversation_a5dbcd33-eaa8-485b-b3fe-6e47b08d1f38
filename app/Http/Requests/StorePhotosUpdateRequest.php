<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePhotosUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'Image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'ResizeImage' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'StoreId' => 'required|string|uuid',
            'IsPrimary' => 'required|in:0,1,true,false',
            'ImageId' => 'nullable|string|uuid',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'Image.required' => 'L\'image originale est requise',
            'Image.image' => 'Le fichier doit être une image',
            'Image.mimes' => 'L\'image doit être au format JPEG, PNG, JPG ou GIF',
            'Image.max' => 'L\'image ne peut pas dépasser 5MB',
            'ResizeImage.required' => 'L\'image redimensionnée est requise',
            'ResizeImage.image' => 'Le fichier redimensionné doit être une image',
            'ResizeImage.mimes' => 'L\'image redimensionnée doit être au format JPEG, PNG, JPG ou GIF',
            'ResizeImage.max' => 'L\'image redimensionnée ne peut pas dépasser 5MB',
            'StoreId.required' => 'L\'ID du magasin est requis',
            'StoreId.string' => 'L\'ID du magasin doit être une chaîne de caractères',
            'StoreId.uuid' => 'L\'ID du magasin doit être un UUID valide',
            'IsPrimary.required' => 'Le statut d\'image principale est requis',
            'IsPrimary.boolean' => 'Le statut d\'image principale doit être un booléen',
            'ImageId.string' => 'L\'ID de l\'image doit être une chaîne de caractères',
            'ImageId.uuid' => 'L\'ID de l\'image doit être un UUID valide',
        ];
    }
}
