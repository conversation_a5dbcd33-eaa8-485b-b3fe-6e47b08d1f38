<?php

namespace App\Http\Requests;

use App\Enums\ScheduleTemplateStatus;
use Illuminate\Foundation\Http\FormRequest;

class ScheduleTemplateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = [
            'name' => [
                'required',
                'string',
                'max:80'
            ],
            'data' => [
                'required',
                'array'
            ],
            'status' => [
                'sometimes',
                'string',
                'in:' . implode(',', ScheduleTemplateStatus::values())
            ]
        ];

        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        
        foreach ($days as $day) {
            $rules["data.{$day}"] = 'array';
            $rules["data.{$day}.morning"] = 'nullable|array';
            $rules["data.{$day}.afternoon"] = 'nullable|array';
            
            if ($this->input("data.{$day}.morning")) {
                $rules["data.{$day}.morning.open"] = 'required|date_format:H:i';
                $rules["data.{$day}.morning.close"] = 'required|date_format:H:i|after:data.{$day}.morning.open';
            }
            
            if ($this->input("data.{$day}.afternoon")) {
                $rules["data.{$day}.afternoon.open"] = 'required|date_format:H:i';
                $rules["data.{$day}.afternoon.close"] = 'required|date_format:H:i|after:data.{$day}.afternoon.open';
            }
        }

        return $rules;
    }

} 