<?php

namespace App\Observers;

use App\Models\Subscription;
use App\Models\SubscriptionLog;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class SubscriptionObserve
{
    /**
     * Handle the Subscription "created" event.
     *
     * @param Subscription $subscription
     * @return void
     */
    public function updating(Subscription $subscription): void
    {
        $watch = ['SubscriptionPlanId', 'StartTime', 'EndTime'];
        $changes = [];

        foreach ($watch as $field) {
            if ($subscription->isDirty($field)) {
                $changes[$field] = [
                    'old' => $subscription->getOriginal($field),
                    'new' => $subscription->{$field},
                ];
            }
        }

        if ($changes) {
            SubscriptionLog::create([
                'SubscriptionId' => $subscription->getAttributes()['Id'],
                'Event' => $changes,
                'Actor' => optional(Auth::user())->name ?? 'system',
                'IpAddress' => request()->ip(),
                'CreatedAt' => Carbon::now(),
            ]);
        }
    }
}
