<?php

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

if (!function_exists('decryptData')) {
    function decryptData($data): bool|string
    {
        $key = base64_decode(config('vikingapp.aes.key'));
        $iv = base64_decode(config('vikingapp.aes.iv'));
        $cipher = "aes-256-cbc";

        return openssl_decrypt($data, $cipher, $key, 0, $iv);
    }
}

if (!function_exists('uniqueFileName')) {
     function uniqueFileName($fileName, $path) {
        if (Storage::exists($path . DIRECTORY_SEPARATOR . $fileName)) {
            $pathInfo = pathinfo($fileName);
            $extension = isset($pathInfo['extension']) ? ('.' . $pathInfo['extension']) : '';

            if (preg_match('/(.*?)(\d+)$/', $pathInfo['filename'], $match)) {
                $base = $match[1];
                $number = intVal($match[2]);
            } else {
                $base = str_replace('&', '', $pathInfo['filename']);
                $number = 0;
            }
            do {
                $fileName = $base . '_' . ++$number.$extension;
            } while (Storage::exists($path.DIRECTORY_SEPARATOR.$fileName));
        }

        return $fileName;
    }
}

if (!function_exists('buildValidCsv')) {
    function buildValidCsv(string $source, array $invalidRows): array
    {
        $validCount = 0;

        if (empty($invalidRows)) {
            $src = new SplFileObject($source);
            $src->setFlags(SplFileObject::READ_CSV | SplFileObject::SKIP_EMPTY);

            foreach ($src as $idx => $cols) {
                if ($cols === false || $cols === [null] || $cols === []) {
                    continue;
                }

                if ($idx > 0) {
                    $validCount++;
                }
            }

            return [
                'filename' => basename($source),
                'valid_count' => $validCount
            ];
        }

        $src = new SplFileObject($source);
        $src->setFlags(SplFileObject::READ_CSV | SplFileObject::SKIP_EMPTY);

        $destPath = Str::replaceLast('.csv', '_valid.csv', $source);

        $dest = fopen($destPath, 'w');

        foreach ($src as $idx => $cols) {
            if ($cols === false || $cols === [null] || $cols === []) {
                continue;
            }

            if ($idx === 0 || !in_array($idx, $invalidRows, true)) {
                fputcsv($dest, $cols);

                if ($idx > 0) {
                    $validCount++;
                }
            }
        }
        fclose($dest);

        return [
            'filename' => basename($destPath),
            'valid_count' => $validCount
        ];
    }
}


if (!function_exists('detectFileType')) {
    /**
     * Detect file type from binary content
     *
     * @param string $content
     * @return string
     */
    function detectFileType(string $content): string
    {
        $signatures = [
            'application/pdf' => "\x25\x50\x44\x46",
            'image/png' => "\x89\x50\x4E\x47",
            'image/jpeg' => "\xFF\xD8\xFF",
            'image/gif' => "GIF89a",
            'application/zip' => "PK\x03\x04",
            'application/msword' => "\xD0\xCF\x11\xE0",
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => "PK\x03\x04",
        ];

        foreach ($signatures as $mimeType => $signature) {
            if (str_starts_with($content, $signature)) {
                return $mimeType;
            }
        }

        return 'application/octet-stream';
    }
}

if (!function_exists('formatFileSize')) {
    /**
     * Format file size in human readable format
     *
     * @param int $bytes
     * @return string
     */
    function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}

if (!function_exists('isImageFile')) {
    /**
     * Check if file is an image based on file type
     *
     * @param string $fileType
     * @return bool
     */
    function isImageFile(string $fileType): bool
    {
        if (str_starts_with($fileType, 'image/')) {
            return true;
        }

        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
        return in_array(strtolower($fileType), $imageExtensions);
    }
}
