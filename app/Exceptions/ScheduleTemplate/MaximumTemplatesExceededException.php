<?php

namespace App\Exceptions\ScheduleTemplate;

use Symfony\Component\HttpFoundation\Response;

class MaximumTemplatesExceededException extends ScheduleTemplateException
{
    public function __construct(string $message = "Maximum de 10 modèles autorisés. Veuillez supprimer un modèle existant avant d'en créer un nouveau.")
    {
        parent::__construct($message, Response::HTTP_BAD_REQUEST);
    }
} 