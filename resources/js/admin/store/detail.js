/**
 * Store Detail Page JavaScript
 * Handles data pre-selection and form interactions for store detail page
 */

class StoreDetailManager {
    constructor() {
        this.storeData = window.storeData || {};
        this.storeServices = window.storeServices || {};
        this.metaData = window.metaData || {};
        this.questions = [];
        this.marketData = (window.metaData && window.metaData.marketInformations) || [];
        this.marketCities = this.marketData.map(m => m.city).filter((v, i, a) => v && a.indexOf(v) === i);
        this.existingMarkets = window.existingMarkets || [];
        this.hasUnsavedChanges = false;
        this.originalFormData = {};
    }

    async init() {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        this.initSelect2();
        this.bindEvents();
        this.preSelectData();
        await this.loadQuestions();
        this.initMarketFields();
        this.initSubscriptionPlanChange();
        this.updatePhotoCounter();
        this.updateNoImagesMessage();
        this.initializeUnsavedChangesTracking();

        setTimeout(() => {
            this.applyProductManufacturingOriginsTranslations();
        }, 500);
    }

    initSelect2() {
        $('.select2').select2({
            theme: 'bootstrap4',
            width: '100%'
        });
    }

    initServicesSelect2() {
        $('#services .select2').select2({
            theme: 'bootstrap4',
            width: '100%',
            placeholder: function() {
                return $(this).data('placeholder');
            }
        });
    }

    bindEvents() {
        $('[data-tab]').on('click', (e) => {
            e.preventDefault();
            this.switchTab($(e.currentTarget));
        });

        $('#save-all-btn').on('click', () => this.saveForm());
        $('#save-basic-info-btn').on('click', () => this.saveBasicInfo());
        $('#save-categories-btn').on('click', () => this.saveCategories());
        $('#add-photo-btn').on('click', () => $('#add-photo-input').click());
        $('#add-photo-input').on('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const existingPhotos = $('#photo-gallery [data-image-id]').length;
                const isPrimary = existingPhotos === 0;
                console.log('Uploading photo. Existing photos:', existingPhotos, 'Setting as primary:', isPrimary);
                this.uploadPhoto(file, isPrimary);
            }
        });

        $(document).on('click', '.replace-image-btn', (e) => {
            const imageId = $(e.target).closest('.replace-image-btn').data('image-id');
            $('#replace-photo-input').data('image-id', imageId).click();
        });

        $('#replace-photo-input').on('change', (e) => {
            const file = e.target.files[0];
            const imageId = $(e.target).data('image-id');
            if (file && imageId) {
                this.replacePhoto(file, imageId);
            }
        });

        $(document).on('click', '.delete-image-btn', (e) => {
            const $btn = $(e.target).closest('.delete-image-btn');
            const imageId = $btn.data('image-id');
            const $imageCard = $btn.closest('[data-image-id]');

            if (confirm('Êtes-vous sûr de vouloir supprimer cette photo ?')) {
                this.deletePhoto(imageId, $imageCard);
            }
        });

        $(document).on('click', '.image-error', (e) => {
            const $errorDiv = $(e.currentTarget);
            const $imageContainer = $errorDiv.closest('.image-container');
            const $img = $imageContainer.find('img');
            const $loadingDiv = $imageContainer.find('.image-loading');

            $errorDiv.hide();
            $loadingDiv.show();

            const originalSrc = $img.attr('src');
            const separator = originalSrc.includes('?') ? '&' : '?';
            const newSrc = originalSrc + separator + 'retry=' + Date.now();

            $img.attr('src', '').attr('src', newSrc);

            setTimeout(() => {
                if ($img.css('opacity') === '0' || $img.css('display') === 'none') {
                    $loadingDiv.hide();
                    $errorDiv.show();
                }
            }, 10000); // 10 second timeout
        });
        $('#save-social-btn').on('click', () => this.saveSocial());
        $('#save-schedules-btn').on('click', () => this.saveSchedules());
        $('#save-description-btn').on('click', () => this.saveDescription());
        $('#save-services-btn').on('click', () => this.saveServices());
        $('#save-answers-btn').on('click', () => this.saveAnswers());

        $('input[type="checkbox"][name*="[closed]"]').on('change', function() {
            const day = $(this).attr('name').match(/hours\[(\w+)\]/)[1];
            const timeInputs = $('input[name*="[' + day + ']"][type="time"]');

            if ($(this).is(':checked')) {
                timeInputs.prop('disabled', true).val('');
            } else {
                timeInputs.prop('disabled', false);
            }
        });

        $('input[name="additional_info[markets]"]').on('change', function() {
            if ($(this).val() === '1' && $(this).is(':checked')) {
                $('#market_info_container').slideDown();
            } else {
                $('#market_info_container').slideUp();
            }
        });

        $('#latitude, #longitude').on('input', (e) => {
            this.validateCoordinateField($(e.target));
        });

        $('#latitude, #longitude').on('blur', (e) => {
            this.validateCoordinateField($(e.target));
        });

        this.bindCategoryEvents();
        this.bindPhotoEvents();
        this.updateNoImagesMessage();
        this.updatePhotoCounter();
    }

    bindCategoryEvents() {
        $('#category-search').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            const categoryItems = $('.category-main-item');

            if (searchTerm === '') {
                categoryItems.show();
                return;
            }

            categoryItems.each(function() {
                const $item = $(this);
                const mainCategoryText = $item.find('.category-main label').text().toLowerCase();
                const subCategoryTexts = $item.find('.subcategory label').map(function() {
                    return $(this).text().toLowerCase();
                }).get();

                const matchesMain = mainCategoryText.includes(searchTerm);
                const matchesSub = subCategoryTexts.some(text => text.includes(searchTerm));

                if (matchesMain || matchesSub) {
                    $item.show();
                    if (!matchesMain && matchesSub) {
                        $item.find('.subcategory-item').each(function() {
                            const subText = $(this).find('label').text().toLowerCase();
                            $(this).toggle(subText.includes(searchTerm));
                        });
                    } else {
                        $item.find('.subcategory-item').show();
                    }
                } else {
                    $item.hide();
                }
            });
        });

        $('#select-all-visible').on('click', function() {
            $('.category-main-item:visible input[type="checkbox"]').prop('checked', true);
            this.updateSelectedCategories();
        }.bind(this));

        $('#deselect-all').on('click', function() {
            $('.category-main-item input[type="checkbox"]').prop('checked', false);
            this.updateSelectedCategories();
        }.bind(this));

        $('#load-popular-categories').on('click', function() {
            alert('La fonctionnalité des catégories populaires serait implémentée ici');
        });

        $(document).on('change', '.sub-category-checkbox', function() {
            const mainCategoryId = $(this).data('main-category');
            const $mainCheckbox = $(`#${mainCategoryId}`);
            const $subCheckboxes = $(`.sub-category-checkbox[data-main-category="${mainCategoryId}"]`);
            const checkedSubCount = $subCheckboxes.filter(':checked').length;
            const totalSubCount = $subCheckboxes.length;

            if (checkedSubCount > 0) {
                $mainCheckbox.prop('checked', true);
            } else {
                $mainCheckbox.prop('checked', false);
            }

            updateSelectedCategories();
        });

        $(document).on('change', '.main-category-checkbox', function() {
            const isChecked = $(this).is(':checked');
            const mainCategoryId = $(this).val();
            $(`.sub-category-checkbox[data-main-category="${mainCategoryId}"]`).prop('checked', isChecked);

            updateSelectedCategories();
        });

        function updateSelectedCategories() {
            const $container = $('#selected-categories-summary');
            $container.empty();

            const selectedCategories = [];

            $('.main-category-checkbox:checked, .sub-category-checkbox:checked').each(function() {
                const $checkbox = $(this);
                const categoryName = $checkbox.data('category-name-translated');
                const categoryId = $checkbox.val();
                const isSubCategory = $checkbox.hasClass('sub-category-checkbox');

                if (isSubCategory) {
                    const mainCategoryId = $checkbox.data('main-category');
                    const mainCategoryName = $(`#${mainCategoryId}`).data('category-name-translated');
                    selectedCategories.push(`${mainCategoryName}: ${categoryName}`);
                } else {
                    const hasSelectedSubs = $(`.sub-category-checkbox[data-main-category="${categoryId}"]:checked`).length > 0;
                    if (!hasSelectedSubs) {
                        selectedCategories.push(categoryName);
                    }
                }
            });

            if (selectedCategories.length === 0) {
                $container.html('<span class="text-muted">Aucune catégorie sélectionnée</span>');
            } else {
                selectedCategories.forEach(categoryText => {
                    $container.append(`<span class="badge bg-primary mr-2 mb-2">${categoryText}</span>`);
                });
            }
        }
    }

    bindPhotoEvents() {
        $('#store_photo').on('change', function() {
            const file = this.files[0];
            const label = $(this).next('.custom-file-label');

            if (file) {
                label.text(file.name);

                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Format de fichier non supporté. Veuillez utiliser JPG, PNG ou GIF.');
                    $(this).val('');
                    label.text('Choisir une image...');
                    $('#image-preview').hide();
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#preview-img').attr('src', e.target.result);
                    $('#image-preview').fadeIn();

                    $('#no-images-message').hide();
                };
                reader.onerror = function() {
                    alert('Erreur lors de la lecture du fichier.');
                };
                reader.readAsDataURL(file);
            } else {
                label.text('Choisir une image...');
                $('#image-preview').hide();

                if ($('#photo-gallery .col-md-4[data-image-id]').length === 0) {
                    $('#no-images-message').show();
                }
            }
        });

        $('#remove-preview-btn').on('click', function() {
            $('#store_photo').val('');
            $('.custom-file-label').text('Choisir une image...');
            $('#image-preview').hide();

            if ($('#photo-gallery .col-md-4[data-image-id]').length === 0) {
                $('#no-images-message').show();
            }
        });

        $('.existing-primary').on('change', function() {
            if ($(this).is(':checked')) {
                $('.existing-primary').each(function() {
                    const label = $(this).next('label');
                    if ($(this).is(':checked')) {
                        label.text('Principale');
                    } else {
                        label.text('Secondaire');
                    }
                });
            }
        });


    }

    switchTab(tabElement) {
        $('.nav-link').removeClass('active');
        $('.tab-panel').removeClass('active').hide();

        tabElement.addClass('active');
        const targetTab = tabElement.attr('data-tab');
        $('#' + targetTab).addClass('active').show();

        if (targetTab === 'services') {
            setTimeout(() => {
                this.initServicesSelect2();
                this.preSelectServices();
            }, 100);
        } else if (targetTab === 'additional-info') {
            setTimeout(() => {
                this.preSelectAdditionalInfo();
                this.applyProductManufacturingOriginsTranslations();
            }, 100);
        }
    }

    preSelectData() {
        this.preSelectServices();
        this.preSelectAdditionalInfo();
        this.checkMarketInfo();
    }

    preSelectServices() {
        if (!this.storeServices) return;

        const selectedServiceIds = [];

        if (Array.isArray(this.storeServices.services)) {
            this.storeServices.services.forEach(service => {
                if (service.id) {
                    selectedServiceIds.push(service.id);
                }
                if (service.subCategories && Array.isArray(service.subCategories)) {
                    service.subCategories.forEach(sub => {
                        if (sub.id) {
                            selectedServiceIds.push(sub.id);
                        }
                    });
                }
            });
        }

        if (this.storeServices.services && typeof this.storeServices.services === 'object' && !Array.isArray(this.storeServices.services)) {
            Object.values(this.storeServices.services).forEach(service => {
                if (service.id) {
                    selectedServiceIds.push(service.id);
                }
            });
        }

        $('select[name^="services_group_"]').each(function() {
            const select = $(this);
            const selectedValues = [];

            select.find('option').each(function() {
                const optionValue = $(this).val();
                if (selectedServiceIds.includes(optionValue)) {
                    selectedValues.push(optionValue);
                }
            });

            if (selectedValues.length > 0) {
                select.val(selectedValues).trigger('change');
            }
        });

        if (this.storeServices.languages && Array.isArray(this.storeServices.languages)) {
            const languageIds = this.storeServices.languages.map(lang => lang.id);
            $('select[name="languages[]"]').val(languageIds).trigger('change');
        }

        if (this.storeServices.paymentMethods && Array.isArray(this.storeServices.paymentMethods)) {
            const paymentIds = this.storeServices.paymentMethods.map(payment => payment.id);
            $('select[name="payment_methods[]"]').val(paymentIds).trigger('change');
        }
    }

    preSelectAdditionalInfo() {
        if (!this.storeData) return;

        this.applyProductManufacturingOriginsTranslations();

        if (this.storeData.productManufacturingOrigins) {
            $('select[name="dynamic_answers[productManufacturingOrigins][]"], select[name="dynamic_answers[productManufacturingOrigins]"]').val(this.storeData.productManufacturingOrigins).trigger('change');
        }

        if (this.storeData.localProducer !== undefined) {
            $(`input[name="dynamic_answers[localProducer]"][value="${this.storeData.localProducer}"]`).prop('checked', true);
        }

        if (this.storeData.averageBudgetPerPerson) {
            $('select[name="dynamic_answers[averageBudgetPerPerson]"]').val(this.storeData.averageBudgetPerPerson).trigger('change');
        }

        if (this.storeData.minimumAge) {
            $('select[name="dynamic_answers[minimumAge]"]').val(this.storeData.minimumAge).trigger('change');
        }

        if (this.storeData.zeroWasteOffer !== undefined) {
            $(`input[name="dynamic_answers[zeroWasteOffer]"][value="${this.storeData.zeroWasteOffer}"]`).prop('checked', true);
        }

        if (this.storeData.partners) {
            const partnerValues = Array.isArray(this.storeData.partners)
                ? this.storeData.partners
                : [this.storeData.partners];
            $('select[name="dynamic_answers[partners][]"]').val(partnerValues).trigger('change');
        }

        if (this.storeData.typeOfCuisines) {
            const cuisineValues = Array.isArray(this.storeData.typeOfCuisines)
                ? this.storeData.typeOfCuisines
                : [this.storeData.typeOfCuisines];
            $('select[name="additional_info[cuisine][]"]').val(cuisineValues).trigger('change');
        }

        if (this.storeData.sellOnStreetMarket !== undefined) {
            $(`input[name="additional_info[markets]"][value="${this.storeData.sellOnStreetMarket}"]`).prop('checked', true);

            if (this.storeData.sellOnStreetMarket == 1 && this.storeData.marketInfo) {
                const marketValues = Array.isArray(this.storeData.marketInfo)
                    ? this.storeData.marketInfo
                    : [this.storeData.marketInfo];
                $('select[name="additional_info[market_info][]"]').val(marketValues).trigger('change');
            }
        }
    }

    checkMarketInfo() {
        if ($('#markets_yes').is(':checked')) {
            $('#market_info_container').show();
        }
    }

    saveForm() {
        let hasScheduleErrors = false;
        $('.schedule-day-container').each(function() {
            const day = $(this).data('day');
            if (typeof window.validateDaySchedule === 'function' && !window.validateDaySchedule(day)) {
                hasScheduleErrors = true;
            }
        });

        if (hasScheduleErrors) {
            this.showAlert('error', 'Veuillez corriger les erreurs dans les horaires avant de sauvegarder toutes les modifications.');
            return;
        }

        const btn = $('#save-all-btn');
        const originalText = btn.html();

        btn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Enregistrement de toutes les modifications...');
        btn.prop('disabled', true);

        this.saveAllDataSequentially()
            .then(() => {
                this.showAlert('success', 'Toutes les modifications ont été sauvegardées avec succès !');

                $(document).trigger('saveCompleted');

                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            })
            .catch((error) => {
                console.error('Error saving all data:', error);
                this.showAlert('error', 'Certaines modifications n\'ont pas pu être sauvegardées. Veuillez vérifier et réessayer.');
            })
            .finally(() => {
                btn.html(originalText);
                btn.prop('disabled', false);
            });
    }

    async saveAllDataSequentially() {
        const saveFunctions = [
            { name: 'Basic Info', fn: () => this.saveBasicInfoPromise() },
            { name: 'Categories', fn: () => this.saveCategoriesPromise() },
            { name: 'Schedules', fn: () => this.saveSchedulesPromise() },
            { name: 'Social Media', fn: () => this.saveSocialPromise() },
            { name: 'Description', fn: () => this.saveDescriptionPromise() },
            { name: 'Services', fn: () => this.saveServicesPromise() },
            { name: 'Additional Info', fn: () => this.saveAnswersPromise() }
        ];

        for (const saveItem of saveFunctions) {
            try {
                await saveItem.fn();
            } catch (error) {
                throw new Error(`Failed to save ${saveItem.name}`);
            }
        }
    }

    saveBasicInfoPromise() {
        const cityValue = $('select[name="city"]').val() || $('#city').val() || '';
        const zipCodeValue = $('select[name="zipCode"]').val() || $('#zip_code').val() || '';

        const sanitizedCity = (cityValue === null || cityValue === undefined || cityValue === 'undefined') ? '' : cityValue;
        const sanitizedZipCode = (zipCodeValue === null || zipCodeValue === undefined || zipCodeValue === 'undefined') ? '' : zipCodeValue;

        const currentData = {
            name: $('input[name="name"]').val() || '',
            addressLine1: $('input[name="addressLine1"]').val() || '',
            addressLine2: $('input[name="addressLine2"]').val() || '',
            phoneNumber: $('input[name="phoneNumber"]').val() || '',
            email: $('input[name="email"]').val() || '',
            city: sanitizedCity,
            zipCode: sanitizedZipCode,
            legalOfficer: $('input[name="legalOfficer"]').val() || '',
            companyRegistrationCode: $('input[name="companyRegistrationCode"]').val() || '',
            apeCode: $('input[name="apeCode"]').val() || '',
            latitude: $('input[name="latitude"]').val() || '',
            longitude: $('input[name="longitude"]').val() || ''
        };

        const originalData = {
            name: this.storeData.name || '',
            addressLine1: this.storeData.addressLine1 || '',
            addressLine2: this.storeData.addressLine2 || '',
            phoneNumber: this.storeData.phoneNumber || '',
            email: this.storeData.email || '',
            city: this.storeData.city || '',
            zipCode: this.storeData.zipCode || '',
            legalOfficer: this.storeData.legalOfficer || '',
            companyRegistrationCode: this.storeData.sirenNumber || '',
            apeCode: this.storeData.apeCode || '',
            latitude: this.storeData.latitude || '0.1',
            longitude: this.storeData.longitude || '-0.1'
        };

        const hasChanges = Object.keys(currentData).some(key => {
            const currentValue = String(currentData[key]).trim();
            const originalValue = String(originalData[key]).trim();

            if ((key === 'latitude' || key === 'longitude') &&
                (originalValue === '0.1' || originalValue === '-0.1' || originalValue === '')) {
                return false;
            }

            return currentValue !== originalValue;
        });

        if (!hasChanges) {
            return $.Deferred().resolve({
                success: true,
                message: 'No changes detected',
                data: null
            }).promise();
        }

        const formData = new FormData();
        formData.append('name', currentData.name);
        formData.append('addressLine1', currentData.addressLine1);
        formData.append('addressLine2', currentData.addressLine2);
        formData.append('city', currentData.city);
        formData.append('zipCode', currentData.zipCode);
        formData.append('legalOfficer', currentData.legalOfficer);
        formData.append('companyRegistrationCode', currentData.companyRegistrationCode);
        formData.append('apeCode', currentData.apeCode);
        formData.append('latitude', typeof currentData.latitude === 'string' ? currentData.latitude : parseFloat(currentData.latitude));
        formData.append('longitude', typeof currentData.longitude === 'string' ? currentData.longitude : parseFloat(currentData.longitude));
        formData.append('phoneNumber', currentData.phoneNumber);
        formData.append('email', currentData.email);
        formData.append('_method', 'PUT');
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        return $.ajax({
            url: window.STORE_API.endpoints.update,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false
        });
    }

    saveDescriptionPromise() {
        const description = $('textarea[name="store[description]"]').val();
        const data = {
            description: description === null ? '' : description
        };

        return $.ajax({
            url: window.STORE_API.endpoints.updateDescription,
            method: 'PUT',
            data: JSON.stringify(data),
            contentType: 'application/json',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    }

    saveServicesPromise() {
        const serviceIds = [];
        $('select[name^="services_group_"] option:selected').each(function() {
            serviceIds.push($(this).val());
        });

        const paymentIds = [];
        $('select[name="payment_methods[]"] option:selected').each(function() {
            paymentIds.push($(this).val());
        });

        const languageIds = [];
        $('select[name="languages[]"] option:selected').each(function() {
            languageIds.push($(this).val());
        });

        const data = {
            serviceIds: serviceIds.length > 0 ? serviceIds : [],
            paymentIds: paymentIds.length > 0 ? paymentIds : [],
            languageIds: languageIds.length > 0 ? languageIds : []
        };

        return $.ajax({
            url: window.STORE_API.endpoints.updateServices,
            method: 'PUT',
            data: JSON.stringify(data),
            contentType: 'application/json',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    }

    saveAnswersPromise() {
        const minimumAge = $('select[name="dynamic_answers[minimumAge]"]').val();
        const localProducer = $('input[name="dynamic_answers[localProducer]"]:checked').val();
        const averageBudget = $('select[name="dynamic_answers[averageBudgetPerPerson]"]').val();
        const sellOnMarket = $('input[name="dynamic_answers[sellOnStreetMarket]"]:checked').val();
        const zeroWaste = $('input[name="dynamic_answers[zeroWasteOffer]"]:checked').val();

        const productManufacturingOrigins = [];
        $('select[name="dynamic_answers[productManufacturingOrigins][]"] option:selected').each(function() {
            const value = parseInt($(this).val());
            if (!isNaN(value)) {
                productManufacturingOrigins.push(value);
            }
        });

        const typeOfCuisines = [];
        $('select[name="additional_info[cuisine][]"] option:selected').each(function() {
            const cuisineId = $(this).val();
            if (cuisineId && cuisineId.trim() !== '') {
                typeOfCuisines.push(cuisineId);
            }
        });

        const partners = [];
        $('select[name="dynamic_answers[partners][]"] option:selected').each(function() {
            const value = parseInt($(this).val());
            if (!isNaN(value)) {
                partners.push(value);
            }
        });

        const markets = [];
        if (sellOnMarket == 1) {
            $('.market-section').each(function() {
                const marketId = $(this).find('.market-name').val();
                const dayOfWeeks = $(this).find('.market-days').val() || [];
                if (marketId) {
                    markets.push({
                        marketInformationId: marketId,
                        dayOfWeeks: dayOfWeeks.map(day => parseInt(day))
                    });
                }
            });
        }

        const data = {
            minimumAge: minimumAge ? parseInt(minimumAge) : 0,
            productManufacturingOrigins: productManufacturingOrigins.length > 0 ? productManufacturingOrigins.map(id => parseInt(id)) : [0],
            localProducer: localProducer ? parseInt(localProducer) : 0,
            typeOfCuisines: typeOfCuisines.length > 0 ? typeOfCuisines.filter(id => id && id.trim() !== '') : [],
            averageBudgetPerPerson: averageBudget ? parseInt(averageBudget) : 0,
            sellOnStreetMarket: sellOnMarket ? parseInt(sellOnMarket) : 0,
            markets: markets.length > 0 ? markets : [],
            zeroWasteOffer: zeroWaste ? parseInt(zeroWaste) : 0,
            partners: partners.length > 0 ? partners.map(id => parseInt(id)) : [0]
        };

        return $.ajax({
            url: window.STORE_API.endpoints.updateAnswers,
            method: 'PUT',
            data: JSON.stringify(data),
            contentType: 'application/json',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    }

    saveBasicInfo() {
        const formData = new FormData();
        const btn = $('#save-basic-info-btn');

        const cityValue = $('select[name="city"]').val() || $('#city').val() || '';
        const zipCodeValue = $('select[name="zipCode"]').val() || $('#zip_code').val() || '';

        const sanitizedCity = (cityValue === null || cityValue === undefined || cityValue === 'undefined') ? '' : cityValue;
        const sanitizedZipCode = (zipCodeValue === null || zipCodeValue === undefined || zipCodeValue === 'undefined') ? '' : zipCodeValue;

        const currentEmail = $('input[name="email"]').val();
        const originalEmail = this.storeData.email || '';

        formData.append('name', $('input[name="name"]').val());
        formData.append('addressLine1', $('input[name="addressLine1"]').val());
        formData.append('addressLine2', $('input[name="addressLine2"]').val());
        formData.append('city', sanitizedCity);
        formData.append('zipCode', sanitizedZipCode);
        formData.append('legalOfficer', $('input[name="legalOfficer"]').val());
        formData.append('companyRegistrationCode', $('input[name="companyRegistrationCode"]').val());
        formData.append('apeCode', $('input[name="apeCode"]').val());
        formData.append('latitude', $('input[name="latitude"]').val());
        formData.append('longitude', $('input[name="longitude"]').val());
        formData.append('phoneNumber', $('input[name="phoneNumber"]').val());

        if (currentEmail !== originalEmail) {
            formData.append('email', currentEmail);
        }

        formData.append('_method', 'PUT');

        this.makeRequest(window.STORE_API.endpoints.update, formData, btn, 'Informations de base mises à jour avec succès !', 'POST', true);
    }

    makeRequest(url, formData, btn, successMessage, method = 'POST', shouldRefresh = false) {
        const originalText = btn.html();

        if (!formData.has('_token')) {
            formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
        }

        btn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Enregistrement...');
        btn.prop('disabled', true);

        $.ajax({
            url: url,
            method: method,
            data: formData,
            processData: false,
            contentType: false,
            success: (response) => {
                if (response.success) {
                    this.showAlert('success', successMessage);

                    $(document).trigger('saveCompleted');

                    if (shouldRefresh) {
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    }
                } else {
                    this.showAlert('error', response.message || 'Échec de la mise à jour');
                }
            },
            error: (xhr) => {
                let errorMessage = 'Une erreur s\'est produite lors de l\'enregistrement';
                if (xhr.responseJSON) {
                    if (xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    if (xhr.responseJSON.errors) {
                        if (Array.isArray(xhr.responseJSON.errors)) {
                            const errorMessages = xhr.responseJSON.errors.map(e => e.errorMessage || JSON.stringify(e));
                            if (errorMessages.length) {
                                errorMessage += ': ' + errorMessages.join(', ');
                            }
                        } else if (typeof xhr.responseJSON.errors === 'object') {
                            const errors = Object.values(xhr.responseJSON.errors).flat();
                            if (errors.length) {
                                errorMessage += ': ' + errors.join(', ');
                            }
                        }
                    }
                }
                this.showAlert('error', errorMessage);
            },
            complete: () => {
                if (!shouldRefresh) {
                    btn.html(originalText);
                    btn.prop('disabled', false);
                } else {
                    setTimeout(() => {
                        btn.html(originalText);
                        btn.prop('disabled', false);
                    }, 1600);
                }
            }
        });
    }

    makeJsonRequest(url, data, btn, successMessage, method = 'POST', shouldRefresh = false) {
        const originalText = btn.html();

        btn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Enregistrement...');
        btn.prop('disabled', true);

        const cleanData = { ...data };
        delete cleanData._method;
        delete cleanData._token;

        $.ajax({
            url: url,
            method: method,
            data: JSON.stringify(cleanData),
            contentType: 'application/json',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: (response) => {
                if (response.success) {
                    this.showAlert('success', successMessage);

                    $(document).trigger('saveCompleted');

                    if (shouldRefresh) {
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    }
                } else {
                    this.showAlert('error', response.message || 'Échec de la mise à jour');
                }
            },
            error: (xhr) => {
                let errorMessage = 'Une erreur s\'est produite lors de l\'enregistrement';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }
                this.showAlert('error', errorMessage);
            },
            complete: () => {
                if (!shouldRefresh) {
                    btn.html(originalText);
                    btn.prop('disabled', false);
                } else {
                    setTimeout(() => {
                        btn.html(originalText);
                        btn.prop('disabled', false);
                    }, 1600);
                }
            }
        });
    }

    saveCategories() {
        const btn = $('#save-categories-btn');

        const selectedCategories = [];
        $('input[name="categories[]"]:checked').each(function() {
            selectedCategories.push($(this).val());
        });

        const data = {
            ids: selectedCategories.length > 0 ? selectedCategories : [],
            _method: 'PUT',
            _token: $('meta[name="csrf-token"]').attr('content')
        };

        this.makeJsonRequest(window.STORE_API.endpoints.updateCategories, data, btn, 'Catégories mises à jour avec succès !', 'PUT');
    }

    saveCategoriesPromise() {
        const selectedCategories = [];
        $('input[name="categories[]"]:checked').each(function() {
            selectedCategories.push($(this).val());
        });

        const data = {
            ids: selectedCategories.length > 0 ? selectedCategories : []
        };

        return $.ajax({
            url: window.STORE_API.endpoints.updateCategories,
            method: 'PUT',
            data: JSON.stringify(data),
            contentType: 'application/json',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    }

    uploadPhoto(file, isPrimary = false) {
        if (!this.validatePhotoFile(file)) return;

        const formData = new FormData();

        this.createResizedImage(file, 0.5, (resizedFile) => {
            formData.append('Image', file);
            formData.append('ResizeImage', resizedFile);

            const storeId = this.getStoreId();
            if (!storeId) return;

            formData.append('StoreId', storeId);
            formData.append('IsPrimary', isPrimary ? '1' : '0');

            console.log('Uploading photo with IsPrimary:', isPrimary ? '1' : '0');

            this.makePhotoRequest(window.STORE_API.endpoints.updatePhotos, formData, null, 'Photo téléchargée avec succès !', (response) => {
                console.log('Photo upload response:', response);
                console.log('Response data:', response.data);
                console.log('isPrimary:', isPrimary);

                if (response.data) {
                    console.log('Calling addPhotoToGallery with:', response.data, isPrimary);
                    this.addPhotoToGallery(response.data, isPrimary);
                    this.updateNoImagesMessage();
                    this.updatePhotoCounter();
                    console.log('Photo added to gallery successfully');
                } else {
                    console.error('No data in photo upload response:', response);
                    this.showAlert('warning', 'Photo téléchargée mais aucune donnée retournée par le serveur');
                }
            });
        });
    }

    replacePhoto(file, imageId) {
        if (!this.validatePhotoFile(file)) return;

        const formData = new FormData();

        this.createResizedImage(file, 0.5, (resizedFile) => {
            formData.append('Image', file);
            formData.append('ResizeImage', resizedFile);

            const storeId = this.getStoreId();
            if (!storeId) return;

            formData.append('StoreId', storeId);
            formData.append('ImageId', imageId);
            formData.append('IsPrimary', '0');

            this.makePhotoRequest(window.STORE_API.endpoints.replacePhoto, formData, null, 'Photo remplacée avec succès !', (response) => {
                console.log('Photo replace response:', response);
                if (response.data) {
                    this.replacePhotoInGallery(imageId, response.data);
                    this.updateNoImagesMessage();
                } else {
                    console.error('No data in photo replace response:', response);
                    this.showAlert('warning', 'Photo remplacée mais aucune donnée retournée par le serveur');
                }
            });
        });
    }

    deletePhoto(imageId, $imageCard) {
        const storeId = this.getStoreId();
        if (!storeId) return;

        const data = {
            storeId: storeId,
            imageId: imageId
        };

        $imageCard.find('.card-body').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Suppression...</div>');

        $.ajax({
            url: window.STORE_API.endpoints.deletePhoto,
            method: 'POST',
            data: data,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: (response) => {
                this.showAlert('success', 'Photo supprimée avec succès !');
                this.removePhotoFromGallery(imageId);
                this.updateNoImagesMessage();
                this.updatePhotoCounter();
            },
            error: (xhr) => {
                console.error('Photo deletion failed:', xhr);
                this.showAlert('error', 'Erreur lors de la suppression de la photo.');
                $imageCard.find('.card-body').html(`
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-danger btn-sm delete-image-btn" data-image-id="${imageId}">
                            <i class="fas fa-trash mr-1"></i>Supprimer
                        </button>
                        <button type="button" class="btn btn-info btn-sm replace-image-btn" data-image-id="${imageId}">
                            <i class="fas fa-sync mr-1"></i>Remplacer
                        </button>
                    </div>
                `);
            }
        });
    }

    validatePhotoFile(file) {
        const maxSize = 5 * 1024 * 1024; // 5MB
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        const minSize = 1024; // 1KB minimum

        if (!file) {
            this.showAlert('error', 'Aucun fichier sélectionné');
            return false;
        }

        console.log('Validating file:', {
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: file.lastModified
        });

        if (file.size < minSize) {
            this.showAlert('error', 'Le fichier est trop petit. Taille minimale: 1KB');
            return false;
        }

        if (file.size > maxSize) {
            const sizeMB = (file.size / (1024 * 1024)).toFixed(2);
            this.showAlert('error', `Le fichier est trop volumineux (${sizeMB}MB). Taille maximale: 5MB`);
            return false;
        }

        if (!allowedTypes.includes(file.type)) {
            this.showAlert('error', `Format de fichier non supporté (${file.type}). Formats acceptés: JPEG, PNG, GIF, WebP`);
            return false;
        }

        const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
        const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
        if (!validExtensions.includes(fileExtension)) {
            this.showAlert('error', `Extension de fichier non valide (${fileExtension}). Extensions acceptées: .jpg, .jpeg, .png, .gif, .webp`);
            return false;
        }

        return true;
    }

    getStoreId() {
        const storeId = window.STORE_API.storeId || window.storeData.id || window.storeData.Id;

        if (!storeId) {
            console.error('Store ID not found in:', {
                STORE_API: window.STORE_API,
                storeData: window.storeData
            });
            this.showAlert('error', 'Erreur: ID du magasin non trouvé.');
            return null;
        }

        return storeId;
    }

    validateImageUrl(url) {
        if (!url || url === '#' || url === '') {
            return false;
        }

        if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../') || url.startsWith('data:')) {
            return true;
        }

        try {
            new URL(url);
            return true;
        } catch (e) {
            if (url.includes('.') && (url.includes('.jpg') || url.includes('.jpeg') || url.includes('.png') || url.includes('.gif') || url.includes('.webp'))) {
                return true;
            }
            console.warn('Potentially invalid image URL:', url, e);
            return false;
        }
    }

    preloadImage(url) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = () => reject(new Error('Failed to load image'));
            img.src = url;
        });
    }

    updateNoImagesMessage() {
        const existingPhotos = $('#photo-gallery [data-image-id]').length;
        const $noImagesMessage = $('#no-images-message');

        if (existingPhotos > 0) {
            $noImagesMessage.hide();
        } else {
            $noImagesMessage.show();
        }
    }

    updatePhotoCounter() {
        const photoCount = $('#photo-gallery [data-image-id]').length;
        const $counter = $('#photo-count');

        $counter.text(`${photoCount} photo${photoCount !== 1 ? 's' : ''}`);

        $counter.removeClass('badge-info badge-warning badge-success badge-danger');

        if (photoCount === 0) {
            $counter.addClass('badge-info');
        } else {
            $counter.addClass('badge-success');
        }

        const $addPhotoCard = $('#add-photo-card');
        $addPhotoCard.show();
    }

    validateCoordinateField($field) {
        const value = parseFloat($field.val());
        const fieldName = $field.attr('id');
        const $feedback = $field.siblings('.invalid-feedback');

        $field.removeClass('is-valid is-invalid');
        $feedback.remove();

        if ($field.val() === '') {
            $field.addClass('is-invalid');
            $field.after('<div class="invalid-feedback">Ce champ est requis</div>');
            return false;
        }

        if (isNaN(value)) {
            $field.addClass('is-invalid');
            $field.after('<div class="invalid-feedback">Veuillez entrer un nombre valide</div>');
            return false;
        }

        let isValid = true;
        let errorMessage = '';

        if (fieldName === 'latitude') {
            if (value < -90 || value > 90) {
                isValid = false;
                errorMessage = 'La latitude doit être comprise entre -90 et 90';
            }
        } else if (fieldName === 'longitude') {
            if (value < -180 || value > 180) {
                isValid = false;
                errorMessage = 'La longitude doit être comprise entre -180 et 180';
            }
        }

        if (!isValid) {
            $field.addClass('is-invalid');
            $field.after(`<div class="invalid-feedback">${errorMessage}</div>`);
            return false;
        }

        return true;
    }

    addPhotoToGallery(photoData, isPrimary) {
        console.log('=== addPhotoToGallery called ===');
        console.log('photoData:', photoData);
        console.log('isPrimary:', isPrimary);

        let imageUrl = photoData.link || photoData.imageUrl || photoData.url || photoData.image_url || photoData.path || photoData.src;
        const imageId = photoData.Id || photoData.imageId || photoData.id || photoData.image_id || Date.now();

        console.log('Extracted imageUrl:', imageUrl);
        console.log('Extracted imageId:', imageId);

        if (!imageUrl && photoData.filename) {
            imageUrl = `/storage/photos/${photoData.filename}`;
            console.log('Constructed URL from filename:', imageUrl);
        } else if (!imageUrl && photoData.name) {
            imageUrl = `/storage/photos/${photoData.name}`;
            console.log('Constructed URL from name:', imageUrl);
        }

        if (!imageUrl || imageUrl === '#' || imageUrl === '') {
            console.error('Invalid image URL provided:', photoData);
            this.showAlert('warning', 'URL de l\'image manquante dans la réponse du serveur. Données reçues: ' + JSON.stringify(photoData));

            imageUrl = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';
            console.log('Using placeholder image');
        }

        const currentPhotoCount = $('#photo-gallery [data-image-id]').length;
        const photoNumber = currentPhotoCount + 1;
        const photoTitle = isPrimary ? 'Photo Principale' : `Photo ${photoNumber}`;

        const photoHtml = `
            <div class="col-md-4 mb-3" data-image-id="${imageId}">
                <div class="card h-100">
                    <div class="position-relative">
                        <div class="image-container position-relative" style="height: 200px; overflow: hidden; background-color: #f8f9fa;">
                            <img src="${imageUrl}"
                                 class="card-img-top store-image"
                                 alt="Photo du magasin"
                                 style="height: 200px; width: 100%; object-fit: cover; transition: opacity 0.3s ease; opacity: 0;"
                                 onload="this.style.opacity='1'; this.parentElement.querySelector('.image-loading').style.display='none !important';"
                                 onerror="this.style.display='none'; this.parentElement.querySelector('.image-error').style.display='flex';">
<!--                            <div class="image-loading position-absolute w-100 h-100 d-flex align-items-center justify-content-center" style="top: 0; left: 0; background: rgba(248,249,250,0.95);">-->
<!--                                <div class="text-center">-->
<!--                                    <i class="fas fa-spinner fa-spin fa-2x mb-2 text-primary"></i>-->
<!--                                    <div class="text-muted small">Chargement de l'image...</div>-->
<!--                                    <div class="progress mt-2" style="width: 80px; height: 3px;">-->
<!--                                        <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary" role="progressbar" style="width: 100%"></div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="image-error position-absolute w-100 h-100 d-flex align-items-center justify-content-center" style="display: none !important; top: 0; left: 0; background: rgba(248,249,250,0.95); cursor: pointer;">
                                <div class="text-center">
                                    <i class="fas fa-exclamation-triangle fa-2x mb-2 text-warning"></i>
                                    <div class="text-danger font-weight-bold small">Erreur de chargement</div>
                                    <small class="text-muted d-block mb-2">Cliquez pour réessayer</small>
                                    <button class="btn btn-sm btn-outline-primary retry-image-btn">
                                        <i class="fas fa-redo mr-1"></i>Réessayer
                                    </button>
                                </div>
                            </div>
                        </div>
                        ${isPrimary ? '<span class="badge badge-primary position-absolute" style="top: 10px; left: 10px; z-index: 10;">Photo Principale</span>' : ''}
                    </div>
                    <div class="card-body p-3 d-flex flex-column">
                        <h6 class="card-title mb-3 text-center">${photoTitle}</h6>
                        <div class="d-flex justify-content-between gap-2 mt-auto">
                            <button type="button" class="btn btn-danger btn-sm delete-image-btn flex-fill" data-image-id="${imageId}" title="Supprimer cette photo">
                                <i class="fas fa-trash mr-1"></i>Supprimer
                            </button>
                            <button type="button" class="btn btn-info btn-sm replace-image-btn flex-fill" data-image-id="${imageId}" title="Remplacer cette photo">
                                <i class="fas fa-sync mr-1"></i>Remplacer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        $('#add-photo-card').before(photoHtml);
        this.updatePhotoNumbers();
        this.updateNoImagesMessage();
        this.updatePhotoCounter();

        console.log('=== addPhotoToGallery completed ===');
    }

    replacePhotoInGallery(imageId, photoData) {
        console.log('Replacing photo in gallery with data:', photoData);

        let imageUrl = photoData.link || photoData.imageUrl || photoData.url || photoData.image_url || photoData.path || photoData.src;

        if (!imageUrl && photoData.filename) {
            imageUrl = `/storage/photos/${photoData.filename}`;
        } else if (!imageUrl && photoData.name) {
            imageUrl = `/storage/photos/${photoData.name}`;
        }

        const $photoCard = $(`[data-image-id="${imageId}"]`);

        if ($photoCard.length && imageUrl && imageUrl !== '#' && imageUrl !== '') {
            const $img = $photoCard.find('img');
            const $imageContainer = $photoCard.find('.image-container');
            const $loadingDiv = $imageContainer.find('.image-loading');
            const $errorDiv = $imageContainer.find('.image-error');

            $img.css('opacity', '0');
            $loadingDiv.show();
            $errorDiv.hide();

            $img.attr('src', imageUrl);

            $img.off('load').on('load', function() {
                $(this).css('opacity', '1');
                $loadingDiv.hide();
                $errorDiv.hide();
            });

            $img.off('error').on('error', function() {
                $(this).hide();
                $loadingDiv.hide();
                $errorDiv.show();
            });
        }
    }

    removePhotoFromGallery(imageId) {
        const $photoToRemove = $(`[data-image-id="${imageId}"]`);
        const wasPrimary = $photoToRemove.find('.badge-primary').length > 0;

        $photoToRemove.fadeOut(300, function() {
            $(this).remove();

            if (wasPrimary) {
                const $firstPhoto = $('#photo-gallery [data-image-id]').first();
                if ($firstPhoto.length) {
                    const $img = $firstPhoto.find('img');
                    if ($img.siblings('.badge-primary').length === 0) {
                        $img.after('<span class="badge badge-primary position-absolute" style="top: 10px; left: 10px;">Photo Principale</span>');
                    }
                    // Update the title to "Photo Principale"
                    $firstPhoto.find('.card-title').text('Photo Principale');
                }
            }

            window.storeDetailManager.updatePhotoNumbers();

            window.storeDetailManager.updateNoImagesMessage();
        });
    }

    updatePhotoNumbers() {
        const $photos = $('#photo-gallery [data-image-id]');
        let photoNumber = 1;

        $photos.each(function() {
            const $photo = $(this);
            const $title = $photo.find('.card-title');
            const isPrimary = $photo.find('.badge-primary').length > 0;

            if (!isPrimary) {
                $title.text(`Photo ${photoNumber}`);
                photoNumber++;
            }
        });
    }

    createResizedImage(file, scaleFactor, callback) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = function() {
            const newWidth = Math.floor(img.width * scaleFactor);
            const newHeight = Math.floor(img.height * scaleFactor);

            canvas.width = newWidth;
            canvas.height = newHeight;

            ctx.drawImage(img, 0, 0, newWidth, newHeight);

            canvas.toBlob(function(blob) {
                const resizedFile = new File([blob], file.name, {
                    type: file.type,
                    lastModified: Date.now()
                });

                callback(resizedFile);
            }, file.type, 0.9);
        };

        img.onerror = function() {
            alert('Erreur lors du redimensionnement de l\'image.');
        };

        img.src = URL.createObjectURL(file);
    }

    makePhotoRequest(url, formData, btn, successMessage, callback = null) {
        if (!formData.has('_token')) {
            formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
        }

        let originalText = '';
        if (btn) {
            originalText = btn.html();
            btn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Téléchargement...');
            btn.prop('disabled', true);
        }

        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            timeout: 30000, // 30 second timeout
            success: (response) => {
                console.log('Photo request success:', response);

                if (response.success) {
                    this.showAlert('success', successMessage);

                    if (callback && typeof callback === 'function') {
                        callback(response);
                    }

                    $('#add-photo-input').val('');
                    $('#replace-photo-input').val('');
                } else {
                    console.error('Photo request failed with response:', response);
                    this.showAlert('error', response.message || 'Échec du téléchargement');
                }
            },
            error: (xhr, status, error) => {
                console.error('Photo request error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });

                let errorMessage = 'Une erreur s\'est produite lors du téléchargement';

                if (xhr.status === 0) {
                    errorMessage = 'Erreur de connexion. Vérifiez votre connexion internet.';
                } else if (xhr.status === 413) {
                    errorMessage = 'Le fichier est trop volumineux. Taille maximale: 5MB.';
                } else if (xhr.status === 422) {
                    errorMessage = 'Erreur de validation. Vérifiez le format du fichier.';
                } else if (xhr.status === 500) {
                    errorMessage = 'Erreur serveur. Veuillez réessayer plus tard.';
                } else if (xhr.responseJSON) {
                    if (xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON.errors) {
                        const errors = Array.isArray(xhr.responseJSON.errors)
                            ? xhr.responseJSON.errors
                            : Object.values(xhr.responseJSON.errors).flat();
                        errorMessage = errors.join(', ');
                    }
                }

                this.showAlert('error', errorMessage);
            },
            complete: () => {
                if (btn) {
                    btn.html(originalText);
                    btn.prop('disabled', false);
                }
            }
        });
    }

    saveSocial() {
        const formData = new FormData();
        const btn = $('#save-social-btn');

        formData.append('siteInternet', $('input[name="social[website]"]').val());
        formData.append('instagram', $('input[name="social[instagram]"]').val());
        formData.append('facebook', $('input[name="social[facebook]"]').val());
        formData.append('linkedIn', $('input[name="social[linkedin]"]').val());
        formData.append('xTwitter', $('input[name="social[twitter]"]').val());
        formData.append('_method', 'PUT');

        this.makeRequest(window.STORE_API.endpoints.updateSocial, formData, btn, 'Réseaux sociaux mis à jour avec succès !');
    }

    saveSocialPromise() {
        const formData = new FormData();

        formData.append('siteInternet', $('input[name="social[website]"]').val());
        formData.append('instagram', $('input[name="social[instagram]"]').val());
        formData.append('facebook', $('input[name="social[facebook]"]').val());
        formData.append('linkedIn', $('input[name="social[linkedin]"]').val());
        formData.append('xTwitter', $('input[name="social[twitter]"]').val());
        formData.append('_method', 'PUT');
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        return $.ajax({
            url: window.STORE_API.endpoints.updateSocial,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false
        });
    }

    saveSchedules() {
        const formData = new FormData();
        const btn = $('#save-schedules-btn');

        const addressId = window.storeData.addressId || window.storeData.address?.id;

        const hoursOfOperations = [];
        const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

        days.forEach((day, index) => {
            const morningOpen = $(`input[name="hours[${day}][morning_open]"]`).val();
            const morningClose = $(`input[name="hours[${day}][morning_close]"]`).val();
            const afternoonOpen = $(`input[name="hours[${day}][afternoon_open]"]`).val();
            const afternoonClose = $(`input[name="hours[${day}][afternoon_close]"]`).val();
            // Map sunday to 0, other days to 1-6
            const dayOfWeek = day === 'sunday' ? 0 : index + 1;
            const daySchedule = {
                dayOfWeek: dayOfWeek,
                morningOpenTime: morningOpen || "",
                morningCloseTime: morningClose || "",
                afternoonOpenTime: afternoonOpen || "",
                afternoonCloseTime: afternoonClose || ""
            };

            hoursOfOperations.push(daySchedule);
        });

        const data = {
            addressId: addressId,
            hoursOfOperations: hoursOfOperations,
            _method: 'PUT',
            _token: $('meta[name="csrf-token"]').attr('content')
        };

        this.makeJsonRequest(window.STORE_API.endpoints.updateSchedules, data, btn, 'Horaires mis à jour avec succès !', 'PUT');
    }

    saveSchedulesPromise() {
        const addressId = window.storeData.addressId || window.storeData.address?.id;

        const hoursOfOperations = [];
        const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

        days.forEach((day, index) => {
            const morningOpen = $(`input[name="hours[${day}][morning_open]"]`).val();
            const morningClose = $(`input[name="hours[${day}][morning_close]"]`).val();
            const afternoonOpen = $(`input[name="hours[${day}][afternoon_open]"]`).val();
            const afternoonClose = $(`input[name="hours[${day}][afternoon_close]"]`).val();
            // Map sunday to 0, other days to 1-6
            const dayOfWeek = day === 'sunday' ? 0 : index + 1;
            const daySchedule = {
                dayOfWeek: dayOfWeek,
                morningOpenTime: morningOpen || "",
                morningCloseTime: morningClose || "",
                afternoonOpenTime: afternoonOpen || "",
                afternoonCloseTime: afternoonClose || ""
            };

            hoursOfOperations.push(daySchedule);
        });

        const data = {
            addressId: addressId,
            hoursOfOperations: hoursOfOperations
        };

        return $.ajax({
            url: window.STORE_API.endpoints.updateSchedules,
            method: 'PUT',
            data: JSON.stringify(data),
            contentType: 'application/json',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    }

    saveDescription() {
        const formData = new FormData();
        const btn = $('#save-description-btn');

        const description = $('textarea[name="store[description]"]').val();
        formData.append('description', description === null ? '' : description);
        formData.append('_method', 'PUT');

        this.makeRequest(window.STORE_API.endpoints.updateDescription, formData, btn, 'Description mise à jour avec succès !');
    }

    saveServices() {
        const btn = $('#save-services-btn');

        const serviceIds = [];
        $('select[name^="services_group_"] option:selected').each(function() {
            serviceIds.push($(this).val());
        });

        const paymentIds = [];
        $('select[name="payment_methods[]"] option:selected').each(function() {
            paymentIds.push($(this).val());
        });

        const languageIds = [];
        $('select[name="languages[]"] option:selected').each(function() {
            languageIds.push($(this).val());
        });

        const data = {
            serviceIds: serviceIds.length > 0 ? serviceIds : [],
            paymentIds: paymentIds.length > 0 ? paymentIds : [],
            languageIds: languageIds.length > 0 ? languageIds : [],
            _method: 'PUT',
            _token: $('meta[name="csrf-token"]').attr('content')
        };

        this.makeJsonRequest(window.STORE_API.endpoints.updateServices, data, btn, 'Services mis à jour avec succès !', 'PUT');
    }

    saveAnswers() {
        const btn = $('#save-answers-btn');

        const minimumAge = $('select[name="dynamic_answers[minimumAge]"]').val();
        const localProducer = $('input[name="dynamic_answers[localProducer]"]:checked').val();
        const averageBudget = $('select[name="dynamic_answers[averageBudgetPerPerson]"]').val();
        const sellOnMarket = $('input[name="dynamic_answers[sellOnStreetMarket]"]:checked').val();
        const zeroWaste = $('input[name="dynamic_answers[zeroWasteOffer]"]:checked').val();

        const productManufacturingOrigins = [];
        $('select[name="dynamic_answers[productManufacturingOrigins][]"] option:selected').each(function() {
            const value = parseInt($(this).val());
            if (!isNaN(value)) {
                productManufacturingOrigins.push(value);
            }
        });

        const typeOfCuisines = [];
        $('select[name="additional_info[cuisine][]"] option:selected').each(function() {
            const cuisineId = $(this).val();
            if (cuisineId && cuisineId.trim() !== '') {
                typeOfCuisines.push(cuisineId);
            }
        });

        const partners = [];
        $('select[name="dynamic_answers[partners][]"] option:selected').each(function() {
            const value = parseInt($(this).val());
            if (!isNaN(value)) {
                partners.push(value);
            }
        });

        const markets = [];
        if (sellOnMarket == 1) {
            $('.market-section').each(function() {
                const marketId = $(this).find('.market-name').val();
                const dayOfWeeks = $(this).find('.market-days').val() || [];
                if (marketId) {
                    markets.push({
                        marketInformationId: marketId,
                        dayOfWeeks: dayOfWeeks.map(day => parseInt(day))
                    });
                }
            });
        }

        const data = {
            minimumAge: minimumAge ? parseInt(minimumAge) : 0,
            productManufacturingOrigins: productManufacturingOrigins.length > 0 ? productManufacturingOrigins.map(id => parseInt(id)) : [0],
            localProducer: localProducer ? parseInt(localProducer) : 0,
            typeOfCuisines: typeOfCuisines.length > 0 ? typeOfCuisines.filter(id => id && id.trim() !== '') : [],
            averageBudgetPerPerson: averageBudget ? parseInt(averageBudget) : 0,
            sellOnStreetMarket: sellOnMarket ? parseInt(sellOnMarket) : 0,
            markets: markets.length > 0 ? markets : [],
            zeroWasteOffer: zeroWaste ? parseInt(zeroWaste) : 0,
            partners: partners.length > 0 ? partners.map(id => parseInt(id)) : [0]
        };

        this.makeJsonRequest(window.STORE_API.endpoints.updateAnswers, data, btn, 'Informations supplémentaires mises à jour avec succès !', 'PUT');
    }



    showAlert(type, message, duration = 5000) {
        const alertTypes = {
            'success': { class: 'alert-success', icon: 'fa-check-circle', color: 'text-success' },
            'error': { class: 'alert-danger', icon: 'fa-exclamation-circle', color: 'text-danger' },
            'warning': { class: 'alert-warning', icon: 'fa-exclamation-triangle', color: 'text-warning' },
            'info': { class: 'alert-info', icon: 'fa-info-circle', color: 'text-info' }
        };

        const alertConfig = alertTypes[type] || alertTypes['info'];

        const alert = $(`
            <div class="alert ${alertConfig.class} alert-dismissible fade show shadow-sm" role="alert" style="border-left: 4px solid currentColor; margin-bottom: 1rem;">
                <div class="d-flex align-items-center">
                    <i class="fas ${alertConfig.icon} mr-3 ${alertConfig.color}" style="font-size: 1.2rem;"></i>
                    <div class="flex-grow-1">
                        <strong>${type.charAt(0).toUpperCase() + type.slice(1)}:</strong> ${message}
                    </div>
                    <button type="button" class="close ml-3" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
        `);

        $('.col-md-9 .alert').remove();
        $('.col-md-9').prepend(alert);
        if (duration > 0) {
            setTimeout(() => {
                alert.fadeOut(() => alert.remove());
            }, duration);
        }

        const alertElement = alert[0];
        if (alertElement && !this.isElementInViewport(alertElement)) {
            alertElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    async loadQuestions() {
        const categoryIds = this.getStoreCategoryIds();
        if (!categoryIds.length) return;
        try {
            const response = await fetch(`/api/Integration/store/${this.storeData.id}/questions`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ categoryId: categoryIds })
            });
            const data = await response.json();
            this.questions = data.data || [];
            this.renderQuestions();
        } catch (e) {
            console.error('Failed to load questions:', e);
        }
    }

    getStoreCategoryIds() {
        if (this.storeData.categories && Array.isArray(this.storeData.categories)) {
            return this.storeData.categories.map(cat => cat.id || cat.categoryId || cat);
        }
        return [];
    }

    renderQuestions() {
        const container = document.getElementById('dynamic-questions-container');
        if (!container) return;
        container.innerHTML = '';
        this.questions.forEach(group => {
            const groupDiv = document.createElement('div');
            groupDiv.className = 'question-group';
            if (group.questionGroupName) {
                const groupTitle = document.createElement('h5');
                groupTitle.textContent = group.questionGroupName;
                groupDiv.appendChild(groupTitle);
            }
            (group.details || []).forEach(q => {
                const qDiv = document.createElement('div');
                qDiv.className = 'form-group';
                const label = document.createElement('label');
                label.textContent = q.question;
                qDiv.appendChild(label);
                if (q.answerTypeName === 'Text') {
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.className = 'form-control';
                    input.name = `dynamic_answers[${q.question}]`;
                    input.value = (this.storeData.answers && this.storeData.answers[q.question]) || '';
                    qDiv.appendChild(input);
                } else if (q.answerTypeName === 'Select' && Array.isArray(q.answers)) {
                    const select = document.createElement('select');
                    select.className = 'form-control';
                    select.name = `dynamic_answers[${q.question}]`;
                    q.answers.forEach(opt => {
                        const option = document.createElement('option');
                        option.value = opt.id;

                        let optionText = opt.name;
                        if (q.question === 'Origin of manufacture of the products?' && window.productManufacturingOriginsTranslations) {
                            optionText = window.productManufacturingOriginsTranslations[opt.name] || opt.name;
                        }
                        option.textContent = optionText;

                        if (this.storeData.answers && this.storeData.answers[q.question] == opt.id) {
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });
                    qDiv.appendChild(select);
                } else if (q.answerTypeName === 'Radio' && Array.isArray(q.answers)) {
                    q.answers.forEach(opt => {
                        const radioDiv = document.createElement('div');
                        radioDiv.className = 'form-check';
                        const input = document.createElement('input');
                        input.type = 'radio';
                        input.className = 'form-check-input';
                        input.name = `dynamic_answers[${q.question}]`;
                        input.value = opt.id;
                        if (this.storeData.answers && this.storeData.answers[q.question] == opt.id) {
                            input.checked = true;
                        }
                        const label = document.createElement('label');
                        label.className = 'form-check-label';
                        label.textContent = opt.name;
                        radioDiv.appendChild(input);
                        radioDiv.appendChild(label);
                        qDiv.appendChild(radioDiv);
                    });
                } else if (q.answerTypeName === 'Checkbox' && Array.isArray(q.answers)) {
                    const selected = (this.storeData.answers && this.storeData.answers[q.question]) || [];
                    q.answers.forEach(opt => {
                        const checkDiv = document.createElement('div');
                        checkDiv.className = 'form-check';
                        const input = document.createElement('input');
                        input.type = 'checkbox';
                        input.className = 'form-check-input';
                        input.name = `dynamic_answers[${q.question}][]`;
                        input.value = opt.id;
                        if (selected.includes && selected.includes(opt.id)) {
                            input.checked = true;
                        }
                        const label = document.createElement('label');
                        label.className = 'form-check-label';
                        label.textContent = opt.name;
                        checkDiv.appendChild(input);
                        checkDiv.appendChild(label);
                        qDiv.appendChild(checkDiv);
                    });
                }
                groupDiv.appendChild(qDiv);
            });
            container.appendChild(groupDiv);
        });

        setTimeout(() => {
            this.applyProductManufacturingOriginsTranslations();
        }, 100);
    }

    applyProductManufacturingOriginsTranslations() {
        const productManufacturingOriginsTranslations = window.productManufacturingOriginsTranslations || {};

        $('.form-group').each(function() {
            const $formGroup = $(this);
            const labelText = $formGroup.find('label').text().trim();

            if (labelText.includes('Origin of manufacture of the products')) {
                const $select = $formGroup.find('select');

                if ($select.length > 0) {
                    const selectedValues = $select.val();

                    $select.find('option').each(function() {
                        const $option = $(this);
                        const originalText = $option.text().trim();
                        if (productManufacturingOriginsTranslations[originalText]) {
                            $option.text(productManufacturingOriginsTranslations[originalText]);
                        }
                    });

                    if ($select.hasClass('select2-hidden-accessible')) {
                        $select.select2('destroy');
                    }
                    $select.select2({
                        theme: 'bootstrap4',
                        width: '100%'
                    });

                    if (selectedValues) {
                        $select.val(selectedValues).trigger('change');
                    }
                }
            }
        });
    }

    initMarketFields() {
        const self = this;
        function populateMarketNamesAndDays(section, initial) {
            const city = section.find('.market-city').val();
            const nameSelect = section.find('.market-name');
            const daysSelect = section.find('.market-days');

            const currentMarketName = !initial ? nameSelect.val() : null;

            nameSelect.empty().append('<option value="">Select an option</option>');
            daysSelect.empty();

            if (!city) {
                nameSelect.prop('disabled', false);
                daysSelect.prop('disabled', true);
                return;
            }

            const markets = self.marketData.filter(m => m.city === city);
            markets.forEach(market => {
                nameSelect.append(
                    $('<option>', {
                        value: market.id,
                        text: market.name
                    })
                );
            });
            nameSelect.prop('disabled', false);

            if (initial) {
                const idx = section.data('market-index');
                const marketName = self.existingMarkets[idx] && self.existingMarkets[idx]['name'] ? self.existingMarkets[idx]['name'] : null;
                if (marketName && nameSelect.find('option[value="' + marketName + '"]').length) {
                    nameSelect.val(marketName);
                }
            }

            const selectedMarketId = nameSelect.val();
            if (selectedMarketId) {
                const selectedMarket = markets.find(m => m.id == selectedMarketId);
                if (selectedMarket && Array.isArray(selectedMarket.dayOfWeeks)) {
                    selectedMarket.dayOfWeeks.forEach(day => {
                        daysSelect.append(
                            $('<option>', {
                                value: day.id,
                                text: day.name
                            })
                        );
                    });
                    daysSelect.prop('disabled', false);

                    if (initial) {
                        const idx = section.data('market-index');
                        const marketDays = self.existingMarkets[idx] && self.existingMarkets[idx]['days'] ? self.existingMarkets[idx]['days'] : [];
                        if (marketDays && marketDays.length) {
                            daysSelect.val(marketDays);
                        }
                    }
                } else {
                    daysSelect.prop('disabled', true);
                }
            } else {
                daysSelect.prop('disabled', true);
            }
        }

        function initializeMarketSections() {
            $('#markets-container .market-section').each(function() {
                const section = $(this);
                populateMarketNamesAndDays(section, true);
            });
            $('#markets-container .select2').select2({ theme: 'bootstrap4', width: '100%' });
        }

        function toggleMarketFields() {
            if ($('input[name="dynamic_answers[sellOnStreetMarket]"]:checked').val() == 1) {
                $('#market-fields-group').show();
                initializeMarketSections();
            } else {
                $('#market-fields-group').hide();
            }
        }
        $('input[name="dynamic_answers[sellOnStreetMarket]"]').on('change', toggleMarketFields);
        toggleMarketFields();

        initializeMarketSections();

        $(document).on('change', '.market-city', function() {
            const section = $(this).closest('.market-section');
            populateMarketNamesAndDays(section, false);

            section.find('.market-name').val('');
            section.find('.market-days').empty().val([]);
        });
        $(document).on('change', '.market-name', function() {
            const section = $(this).closest('.market-section');
            const selectedMarketId = $(this).val();
            const city = section.find('.market-city').val();
            const daysSelect = section.find('.market-days');

            daysSelect.empty();

            if (selectedMarketId && city) {
                const markets = self.marketData.filter(m => m.city === city);
                const selectedMarket = markets.find(m => m.id == selectedMarketId);

                if (selectedMarket && Array.isArray(selectedMarket.dayOfWeeks)) {
                    selectedMarket.dayOfWeeks.forEach(day => {
                        daysSelect.append(
                            $('<option>', {
                                value: day.id,
                                text: day.name
                            })
                        );
                    });
                    daysSelect.prop('disabled', false);
                } else {
                    daysSelect.prop('disabled', true);
                }
            } else {
                daysSelect.prop('disabled', true);
            }

            daysSelect.val([]).trigger('change');
        });

        $('#add-market').on('click', function() {
            const index = $('#markets-container .market-section').length;
            const newSection = $(
                `<div class="market-section border rounded p-3 mb-3 bg-light" data-market-index="${index}">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Market ${index + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-market">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="form-group">
                        <label>Market City Name *</label>
                        <select class="form-control select2 market-city" name="markets[${index}][city]">
                            <option value="">Select an option</option>
                            ${self.marketCities.map(city => `<option value="${city}">${city}</option>`).join('')}
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Market Name *</label>
                        <select class="form-control select2 market-name" name="markets[${index}][name]">
                            <option value="">Select an option</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Market Day *</label>
                        <select class="form-control select2 market-days" name="markets[${index}][days][]" multiple></select>
                    </div>
                </div>`
            );
            $('#markets-container').append(newSection);
            newSection.find('.select2').select2({ theme: 'bootstrap4', width: '100%' });
            $('#markets-container .remove-market').show();
        });

        $(document).on('click', '.remove-market', function() {
            $(this).closest('.market-section').remove();
            $('#markets-container .market-section').each(function(i) {
                $(this).attr('data-market-index', i);
                $(this).find('h6').text('Marché ' + (i + 1));
            });
            if ($('#markets-container .market-section').length === 1) {
                $('#markets-container .remove-market').hide();
            }
        });

        if ($('#markets-container .market-section').length === 1) {
            $('#markets-container .remove-market').hide();
        }
    }

    initSubscriptionPlanChange() {
        let selectedPlanType = null;
        let selectedPlanName = null;
        let currentPlanName = $(".badge.p-2.mb-2").text().trim();
        let membershipPlans = ["Bronze", "Silver", "Gold", "Platinum"];

        $(document).on('click', '.subscription-plan-option', (e) => {
            e.preventDefault();
            selectedPlanType = $(e.currentTarget).data('plan-type');
            selectedPlanName = $(e.currentTarget).data('plan-name');

            let isCurrentMembership = membershipPlans.includes(currentPlanName);
            let isTargetMembership = membershipPlans.includes(selectedPlanName);

            if (!isCurrentMembership && isTargetMembership) {
                $('#confirmAssignNewPlanModal').modal('show');
            } else if (isCurrentMembership && isTargetMembership) {
                $('#confirmChangeOrRemovePlanModal').modal('show');
            } else if (isCurrentMembership && !isTargetMembership) {
                $('#confirmChangeOrRemovePlanModal').modal('show');
            }
        });

        $(document).on('click', '#confirmAssignNewPlan', () => {
            this.submitPlanChange(selectedPlanType);
        });
        $(document).on('click', '#confirmChangeOrRemovePlan', () => {
            this.submitPlanChange(selectedPlanType);
        });
    }

    showLoadingOverlay() {
        $('#loadingOverlay').show();
    }

    hideLoadingOverlay() {
        $('#loadingOverlay').hide();
    }

    submitPlanChange(selectedPlanType) {
        if (selectedPlanType === null || selectedPlanType === undefined) {
            return;
        }

        const storeId = window.STORE_API.storeId;
        const url = window.STORE_API.endpoints.updateSubscription || `/store/${storeId}/update-subscription`;

        this.showLoadingOverlay();

        $.ajax({
            url: url.replace(':id', storeId),
            type: 'PUT',
            data: { plan: selectedPlanType },
            beforeSend: function () {
                $('.modal').modal('hide');
            },
            success: (res) => {
                window.location.reload();
            },
            error: (xhr, status, error) => {
                let msg = 'Erreur lors de la mise à jour du plan d\'abonnement';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    msg = xhr.responseJSON.message;
                }
                alert(msg);
            },
            complete: function() {
                this.hideLoadingOverlay();
            }.bind(this)
        });
    }

    initializeUnsavedChangesTracking() {
        this.captureOriginalFormData();

        $(document).on('input change', 'input, textarea, select', () => {
            this.checkForUnsavedChanges();
        });

        $(document).on('photoUploaded photoDeleted', () => {
            this.hasUnsavedChanges = true;
        });

        $(window).on('beforeunload', (e) => {
            if (this.hasUnsavedChanges) {
                const message = 'Vos modifications ne seront pas enregistrées. Voulez-vous quitter ?';
                e.returnValue = message;
                return message;
            }
        });

        $(document).on('click', 'a[href]:not([href^="#"]):not([target="_blank"])', (e) => {
            if (this.hasUnsavedChanges) {
                e.preventDefault();
                const targetUrl = $(e.target).closest('a').attr('href');
                this.showUnsavedChangesModal(targetUrl);
            }
        });

        $(document).on('saveCompleted', () => {
            this.hasUnsavedChanges = false;
            this.captureOriginalFormData();
        });
    }

    captureOriginalFormData() {
        this.originalFormData = {};

        $('input, textarea, select').each((index, element) => {
            const $element = $(element);
            const name = $element.attr('name') || $element.attr('id');
            if (name) {
                if ($element.is(':checkbox') || $element.is(':radio')) {
                    this.originalFormData[name] = $element.is(':checked');
                } else {
                    this.originalFormData[name] = $element.val();
                }
            }
        });
    }

    checkForUnsavedChanges() {
        let hasChanges = false;

        $('input, textarea, select').each((index, element) => {
            const $element = $(element);
            const name = $element.attr('name') || $element.attr('id');
            if (name && this.originalFormData.hasOwnProperty(name)) {
                let currentValue;
                if ($element.is(':checkbox') || $element.is(':radio')) {
                    currentValue = $element.is(':checked');
                } else {
                    currentValue = $element.val();
                }

                if (this.originalFormData[name] !== currentValue) {
                    hasChanges = true;
                    return false;
                }
            }
        });

        this.hasUnsavedChanges = hasChanges;
    }

    showUnsavedChangesModal(targetUrl) {
        const modalHtml = `
            <div class="modal fade" id="unsavedChangesModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-body text-center p-4">
                            <div class="mb-3">
                                <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect width="60" height="60" rx="30" fill="#FFEFCF"/>
                                    <path d="M40.6875 33.4875L32.6625 20.575C32.025 19.7125 31.05 19.225 30 19.225C28.9125 19.225 27.9375 19.7125 27.3375 20.575L19.3125 33.4875C18.5625 34.5 18.45 35.8125 19.0125 36.9375C19.575 38.0625 20.7 38.775 21.975 38.775H38.025C39.3 38.775 40.425 38.0625 40.9875 36.9375C41.55 35.85 41.4375 34.5 40.6875 33.4875ZM39.4875 36.1875C39.1875 36.75 38.6625 37.0875 38.025 37.0875H21.975C21.3375 37.0875 20.8125 36.75 20.5125 36.1875C20.25 35.625 20.2875 34.9875 20.6625 34.5L28.6875 21.5875C28.9875 21.175 29.475 20.9125 30 20.9125C30.525 20.9125 31.0125 21.1375 31.3125 21.5875L39.3375 34.5C39.7125 34.9875 39.75 35.625 39.4875 36.1875Z" fill="#CB8E32"/>
                                    <path d="M30.0002 26.2C29.5502 26.2 29.1377 26.575 29.1377 27.0625V31.15C29.1377 31.6 29.5127 32.0125 30.0002 32.0125C30.4877 32.0125 30.8627 31.6375 30.8627 31.15V27.025C30.8627 26.575 30.4502 26.2 30.0002 26.2Z" fill="#CB8E32"/>
                                    <path d="M30.0002 33C29.5502 33 29.1377 33.375 29.1377 33.8625V34.05C29.1377 34.5 29.5127 34.9125 30.0002 34.9125C30.4877 34.9125 30.8627 34.5375 30.8627 34.05V33.825C30.8627 33.375 30.4502 33 30.0002 33Z" fill="#CB8E32"/>
                                </svg>
                            </div>
                            <h4 class="modal-title font-weight-bold mb-3">Modifications non enregistrées</h4>
                            <div class="text-muted mb-4">
                                <p>Vos modifications ne seront pas enregistrées. Voulez-vous quitter ?</p>
                            </div>
                            <div class="d-flex justify-content-center gap-3">
                                <button type="button" class="btn btn-outline-secondary px-4" data-dismiss="modal">Non</button>
                                <button type="button" class="btn btn-success px-4" style="background-color: #CA8D31; border-color: #CA8D31;" id="confirmLeave">Oui</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('#unsavedChangesModal').remove();

        $('body').append(modalHtml);

        $('#unsavedChangesModal').modal('show');

        $('#confirmLeave').on('click', () => {
            this.hasUnsavedChanges = false;
            $('#unsavedChangesModal').modal('hide');
            window.location.href = targetUrl;
        });
    }
}

$(document).ready(() => {
    const storeManager = new StoreDetailManager();
    storeManager.init();
});
